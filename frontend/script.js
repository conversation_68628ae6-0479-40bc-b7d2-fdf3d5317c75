// 日志控制系统 - 生产环境日志优化
const PRODUCTION_MODE = true;  // 生产环境设为true
const DEBUG_UPLOAD = false;    // 上传调试日志
const DEBUG_PAYMENT = false;   // 支付调试日志
const DEBUG_POLLING = false;   // 轮询调试日志
const DEBUG_BROWSER = false;   // 浏览器兼容性调试

// 条件日志函数
function debugLog(message, category = 'general') {
    if (!PRODUCTION_MODE) {
        console.log(message);
    } else {
        // 生产环境只显示重要信息
        if (category === 'important' || category === 'error') {
            console.log(message);
        }
    }
}

// 创建一个超强化的登录状态管理器，防止扩展干扰
window.AuthManager = {
    _token: null,
    _backupToken: null,
    _initialized: false,
    _storageKey: 'token',
    _backupKey: 'auth_backup_' + Math.random().toString(36).substr(2, 9),
    _originalRemoveItem: localStorage.removeItem,
    _originalClear: localStorage.clear,

    init() {
        if (this._initialized) return;

        // 从多个位置尝试恢复token
        this._token = localStorage.getItem(this._storageKey) ||
                     sessionStorage.getItem(this._storageKey) ||
                     localStorage.getItem(this._backupKey);

        this._backupToken = this._token;
        this._initialized = true;

        console.log('AuthManager 初始化，token状态:', !!this._token);

        // 启动保护机制
        this._startProtection();
    },

    setToken(token) {
        this._token = token;
        this._backupToken = token;

        // 多重保存
        try {
            localStorage.setItem(this._storageKey, token);
            sessionStorage.setItem(this._storageKey, token);
            localStorage.setItem(this._backupKey, token);
        } catch (e) {
            console.warn('保存token时出错:', e);
        }

        debugLog('Token已设置并备份', 'debug');
    },

    getToken() {
        if (!this._initialized) this.init();

        // 多重检查和恢复
        const storageToken = localStorage.getItem(this._storageKey);
        const sessionToken = sessionStorage.getItem(this._storageKey);
        const backupToken = localStorage.getItem(this._backupKey);

        // 如果主token丢失，尝试从备份恢复
        if (!this._token && (storageToken || sessionToken || backupToken)) {
            this._token = storageToken || sessionToken || backupToken;
            debugLog('从备份恢复token', 'debug');
        }

        // 如果内存token存在但存储中的丢失，恢复存储
        if (this._token && !storageToken) {
            debugLog('检测到localStorage token被清除，正在恢复...', 'debug');
            try {
                localStorage.setItem(this._storageKey, this._token);
            } catch (e) {
                console.warn('恢复localStorage token失败:', e);
            }
        }

        return this._token;
    },

    clearToken() {
        console.log('🚪 开始执行退出操作');

        // 先清除内存中的token
        this._token = null;
        this._backupToken = null;

        // 清除所有位置的token
        try {
            // 使用保存的原始方法，绕过保护
            this._originalRemoveItem.call(localStorage, this._storageKey);
            sessionStorage.removeItem(this._storageKey);
            this._originalRemoveItem.call(localStorage, this._backupKey);

            debugLog('✅ Token已完全清除', 'debug');
        } catch (e) {
            console.warn('清除token时出错:', e);
        }
    },

    isLoggedIn() {
        const token = this.getToken();
        if (!token) return false;

        // 检查token是否过期
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Math.floor(Date.now() / 1000);

            if (payload.exp && payload.exp < currentTime) {
                console.log('Token已过期，清除token');
                this.clearToken();
                return false;
            }

            return true;
        } catch (e) {
            console.warn('Token格式无效，清除token:', e);
            this.clearToken();
            return false;
        }
    },

    // 验证token有效性（包括过期检查）
    validateToken(token = null) {
        const tokenToValidate = token || this.getToken();
        if (!tokenToValidate) return false;

        try {
            const payload = JSON.parse(atob(tokenToValidate.split('.')[1]));
            const currentTime = Math.floor(Date.now() / 1000);

            // 检查是否过期
            if (payload.exp && payload.exp < currentTime) {
                return false;
            }

            // 检查是否即将过期（5分钟内）
            if (payload.exp && (payload.exp - currentTime) < 300) {
                console.warn('Token即将过期，建议刷新');
            }

            return true;
        } catch (e) {
            return false;
        }
    },

    // 启动超强保护机制
    _startProtection() {
        // 保护localStorage.removeItem方法
        localStorage.removeItem = (key) => {
            if (key === this._storageKey) {
                // 检查调用栈，只允许我们自己的clearToken方法删除
                const stack = new Error().stack;
                if (stack && stack.includes('clearToken')) {
                    console.log('✅ 允许正常退出删除token');
                    return this._originalRemoveItem.call(localStorage, key);
                } else {
                    console.log('🛡️ 阻止外部删除token');
                    return; // 阻止删除
                }
            }
            return this._originalRemoveItem.call(localStorage, key);
        };

        // 保护localStorage.clear方法
        localStorage.clear = () => {
            const token = localStorage.getItem(this._storageKey);
            this._originalClear.call(localStorage);
            if (token && this._backupToken) {
                console.log('🛡️ localStorage被清空，恢复token');
                localStorage.setItem(this._storageKey, this._backupToken);
            }
        };

        // 每200ms检查一次token状态（更频繁）
        setInterval(() => {
            if (this._backupToken && !localStorage.getItem(this._storageKey)) {
                console.log('🛡️ 检测到token被外部清除，正在恢复...');
                try {
                    localStorage.setItem(this._storageKey, this._backupToken);
                    this._token = this._backupToken;
                } catch (e) {
                    console.warn('自动恢复token失败:', e);
                }
            }
        }, 200);

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this._backupToken) {
                const currentToken = localStorage.getItem(this._storageKey);
                if (!currentToken) {
                    console.log('🛡️ 页面重新可见时恢复token');
                    try {
                        localStorage.setItem(this._storageKey, this._backupToken);
                        this._token = this._backupToken;
                    } catch (e) {
                        console.warn('页面可见时恢复token失败:', e);
                    }
                }
            }
        });

        // 监听焦点变化
        window.addEventListener('focus', () => {
            if (this._backupToken && !localStorage.getItem(this._storageKey)) {
                console.log('🛡️ 窗口获得焦点时恢复token');
                try {
                    localStorage.setItem(this._storageKey, this._backupToken);
                    this._token = this._backupToken;
                } catch (e) {
                    console.warn('焦点时恢复token失败:', e);
                }
            }
        });
    }
};

// 超强错误捕获机制，完全阻止扩展干扰
window.addEventListener('error', (e) => {
    // 阻止所有扩展相关的错误
    if (e.filename && (
        e.filename.includes('extension://') ||
        e.filename.includes('chrome-extension://') ||
        e.filename.includes('contentScript.js') ||
        e.filename.includes('utils.js') ||
        e.filename.includes('request.js') ||
        e.filename.includes('jquery.js')
    )) {
        console.log('🛡️ 阻止扩展错误:', e.message);
        e.stopPropagation();
        e.preventDefault();
        return false;
    }

    // 阻止包含特定关键词的错误
    if (e.message && (
        e.message.includes('yhchj.com') ||
        e.message.includes('api.yhchj.com') ||
        e.message.includes('yhc_get_uuid') ||
        e.message.includes('XMLHttpRequest')
    )) {
        console.log('🛡️ 阻止网络相关错误:', e.message);
        e.stopPropagation();
        e.preventDefault();
        return false;
    }
});

// 防止未捕获的Promise错误影响页面
window.addEventListener('unhandledrejection', (e) => {
    if (e.reason) {
        const reason = e.reason.toString();
        if (reason.includes('yhchj.com') ||
            reason.includes('api.yhchj.com') ||
            reason.includes('XMLHttpRequest') ||
            reason.includes('CORS') ||
            reason.includes('net::ERR_FAILED')) {
            console.log('🛡️ 阻止Promise网络错误:', reason);
            e.stopPropagation();
            e.preventDefault();
            return false;
        }
    }
});

// 覆盖console方法以过滤扩展错误
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;

console.error = function(...args) {
    const message = args.join(' ');
    if (message.includes('yhchj.com') ||
        message.includes('api.yhchj.com') ||
        message.includes('extension://') ||
        message.includes('contentScript.js') ||
        message.includes('XMLHttpRequest') ||
        message.includes('CORS') ||
        message.includes('net::ERR_FAILED') ||
        message.includes('Failed to load resource')) {
        return; // 静默处理扩展错误
    }
    originalConsoleError.apply(console, args);
};

console.warn = function(...args) {
    const message = args.join(' ');
    if (message.includes('yhchj.com') ||
        message.includes('api.yhchj.com') ||
        message.includes('CORS') ||
        message.includes('XMLHttpRequest')) {
        return; // 静默处理扩展警告
    }
    originalConsoleWarn.apply(console, args);
};

// 拦截网络错误
const originalFetch = window.fetch;
window.fetch = function(...args) {
    const url = args[0];
    if (typeof url === 'string' && url.includes('yhchj.com')) {
        // 静默阻止扩展的网络请求
        return Promise.reject(new Error('Blocked extension request'));
    }
    return originalFetch.apply(this, args);
};

// 全局变量声明（必须在使用前声明）
let currentDocument = null;
let activeTasks = {};
let pollInterval = null;

document.addEventListener('DOMContentLoaded', () => {
    console.log('WriterPro 页面加载完成');

    // 初始化支付方式显示
    initializePaymentMethods();

    // 初始化认证管理器
    try {
        window.AuthManager.init();
        console.log('AuthManager初始化成功，当前登录状态:', window.AuthManager.isLoggedIn());

        // 输出调试信息
        const token = window.AuthManager.getToken();
        if (token) {
            console.log('发现已保存的token:', token.substring(0, 20) + '...');
            // 检查免费优化权限
            checkFreeOptimizePermission();
        } else {
            console.log('未发现已保存的token');
        }
    } catch (e) {
        console.error('AuthManager初始化失败:', e);
        // 即使初始化失败，也要确保基本功能可用
        window.AuthManager._initialized = true;
    }

    // --- 通用文档内容恢复逻辑 ---
    // 首先检查是否有保存的文档内容，无论是否是支付成功后的跳转
    function restoreDocumentContent() {
        let documentToRestore = null;

        // 优先从pendingDocument恢复（支付成功后的临时状态）
        const pendingDocumentStr = localStorage.getItem('pendingDocument');
        if (pendingDocumentStr) {
            try {
                documentToRestore = JSON.parse(pendingDocumentStr);
                debugLog('📄 从pendingDocument恢复文档状态:', documentToRestore.name, 'debug');

                // 将临时文档保存为持久化文档
                localStorage.setItem('currentDocument', JSON.stringify(documentToRestore));
                // 清除临时文档信息
                localStorage.removeItem('pendingDocument');
            } catch (error) {
                console.error('解析pendingDocument失败:', error);
            }
        }

        // 如果没有临时文档，尝试从持久化文档恢复
        if (!documentToRestore) {
            const currentDocumentStr = localStorage.getItem('currentDocument');
            if (currentDocumentStr) {
                try {
                    documentToRestore = JSON.parse(currentDocumentStr);
                    debugLog('📄 从currentDocument恢复文档状态:', documentToRestore.name, 'debug');
                } catch (error) {
                    console.error('解析currentDocument失败:', error);
                }
            }
        }

        // 如果找到了要恢复的文档，恢复到界面
        if (documentToRestore) {
            // 设置全局文档变量
            window.currentDocument = {
                id: documentToRestore.id,
                name: documentToRestore.name,
                content: documentToRestore.content,
                uploadTime: documentToRestore.uploadTime
            };
            currentDocument = window.currentDocument;

            // 延迟执行，确保DOM元素已加载
            setTimeout(() => {
                // 只有在有文档内容需要恢复时才切换到粘贴文本标签页
                if (documentToRestore.content && documentToRestore.content.trim()) {
                    // 设置恢复标记，允许在切换标签页时恢复内容
                    localStorage.setItem('shouldRestoreTextContent', 'true');

                    // 切换到粘贴文本标签页
                    switchToTab('text');

                    // 设置文档内容到文本编辑器
                    setTimeout(() => {
                        const textarea = document.getElementById('inputText');
                        if (textarea && documentToRestore.content) {
                            textarea.value = documentToRestore.content;
                            console.log('📝 已恢复文档内容到粘贴文本编辑器，内容长度:', documentToRestore.content.length);

                            // 触发编辑器更新事件
                            const event = new Event('input', { bubbles: true });
                            textarea.dispatchEvent(event);

                            // 更新字数统计
                            if (typeof updatePriceDisplay === 'function') {
                                updatePriceDisplay();
                            }
                        }
                    }, 200);
                } else {
                    // 没有内容需要恢复时，默认停留在上传文档页面
                    console.log('📄 没有内容需要恢复，保持默认的上传文档页面');
                }
            }, 500);
        }

        return documentToRestore;
    }

    // 执行通用文档内容恢复
    const restoredDocument = restoreDocumentContent();

    // 检查是否从支付页面返回
    const paymentRedirectFrom = localStorage.getItem('paymentRedirectFrom');
    if (paymentRedirectFrom && window.location.href === paymentRedirectFrom) {
        console.log('📱 检测到从支付页面返回，检查支付状态');
        localStorage.removeItem('paymentRedirectFrom');

        // 检查服务类型，确保只有内容优化服务才执行自动优化
        const lastSelectedService = localStorage.getItem('lastSelectedService');
        console.log('🔍 页面刷新检查服务类型:', lastSelectedService);

        if (lastSelectedService === 'ai') {
            console.log('🤖 确认为A内容优化服务，延迟检查支付状态');
            // 延迟检查支付状态，给支付回调时间处理
            setTimeout(() => {
                verifyPaymentAndStartOptimization();
            }, 2000);
        } else if (lastSelectedService === 'expert') {
            console.log('🎓 检测到学术专家服务，清理状态不执行优化');
            // 清理状态，学术专家服务不需要自动优化
            localStorage.removeItem('lastPaymentId');
            localStorage.removeItem('lastSelectedService');
        } else {
            console.log('⚠️ 未知服务类型或无服务类型，跳过支付检查');
        }
    }

    // 现在支持弹窗和页面跳转两种支付方式
    // 支付成功后会自动开始优化

    // 监听来自支付窗口的消息（仅作为提示，真实验证在服务端）
    window.addEventListener('message', function(event) {
        // 过滤掉DOCX库的内部消息和其他无关消息
        if (event.data &&
            typeof event.data === 'string' &&
            event.data.startsWith('setImmediate$')) {
            return; // 忽略setImmediate消息
        }

        // 只记录有意义的消息
        if (event.data &&
            (event.data.type === 'PAYMENT_SUCCESS' ||
             event.data.type === 'PAYMENT_FAILED' ||
             event.data.type === 'PAYMENT_CANCELLED')) {
            console.log('📨 收到支付相关消息:', event.data);
        }

        if (event.data && event.data.type === 'PAYMENT_SUCCESS') {
            // 删除：不再显示支付成功验证提示

            // 检查是否是学术专家服务
            const lastSelectedService = localStorage.getItem('lastSelectedService');
            console.log('💼 支付成功后检查服务类型:', lastSelectedService);

            if (lastSelectedService === 'expert') {
                // 学术专家服务：显示全屏联系弹窗
                console.log('🎓 显示学术专家服务联系弹窗');
                setTimeout(() => {
                    showExpertServiceFullscreenModal();
                    // 清理状态，防止影响其他页面
                    setTimeout(() => {
                        console.log('🧹 清理学术专家服务状态');
                        localStorage.removeItem('lastPaymentId');
                        localStorage.removeItem('lastSelectedService');
                    }, 2000);
                }, 1000);
            } else if (lastSelectedService === 'ai') {
                // 内容优化服务：验证支付状态并开始优化
                console.log('🤖 开始内容优化服务验证');
                setTimeout(() => {
                    verifyPaymentAndStartOptimization();
                }, 1000);
            } else {
                console.log('⚠️ 未知服务类型，跳过处理');
            }
        }
    });

    // 安全的支付验证和优化启动
    async function verifyPaymentAndStartOptimization() {
        console.log('🔐 开始服务端支付验证');

        try {
            // 首先检查服务类型，确保只有内容优化服务才执行自动优化
            const lastSelectedService = localStorage.getItem('lastSelectedService');
            console.log('🎯 当前选择的服务类型:', lastSelectedService);

            if (lastSelectedService === 'expert') {
                console.log('⚠️ 检测到学术专家服务，不执行自动优化');
                showNotification('学术专家服务已完成，请联系客服获取服务', 'info');
                return;
            }

            if (lastSelectedService !== 'ai') {
                console.log('⚠️ 未检测到内容优化服务类型，跳过自动优化');
                return;
            }

            const headers = getAuthHeaders();
            if (!headers) {
                throw new Error('请先登录');
            }

            const lastPaymentId = localStorage.getItem('lastPaymentId');
            if (!lastPaymentId) {
                throw new Error('没有找到支付ID');
            }

            // 调用后端验证支付状态
            const response = await fetch(`${API_BASE_URL}/payments/verify/${lastPaymentId}`, {
                method: 'GET',
                headers: headers
            });

            if (!response.ok) {
                throw new Error('验证支付状态失败');
            }

            const data = await response.json();
            console.log('💰 服务端支付验证结果:', data);

            if (data.success && data.verified && data.status === 'paid') {
                // 再次确认订单类型（如果后端返回了订单信息）
                if (data.orderType && data.orderType === 'expert') {
                    console.log('⚠️ 后端确认为学术专家服务，不执行自动优化');
                    showNotification('学术专家服务已完成，请联系客服获取服务', 'info');
                    // 清理状态，防止重复触发
                    localStorage.removeItem('lastPaymentId');
                    localStorage.removeItem('lastSelectedService');
                    return;
                }

                console.log('✅ 确认为内容优化服务，开始自动优化');
                showNotification('支付验证成功！正在开始优化...', 'success');

                setTimeout(() => {
                    startAutoOptimization();
                    // 清理状态，防止重复触发
                    localStorage.removeItem('lastPaymentId');
                    localStorage.removeItem('lastSelectedService');
                }, 500);
            } else {
                console.warn('⚠️ 支付验证失败:', data.message || '支付状态异常');
                showNotification('支付验证失败，请联系客服', 'error');
            }

        } catch (error) {
            console.error('❌ 支付验证失败:', error);
            showNotification('支付验证失败: ' + error.message, 'error');
        }
    }

    // --- 浏览器弹窗支持检测 ---
    function checkPopupSupport() {
        try {
            const testWindow = window.open('', '_blank', 'width=1,height=1');
            if (testWindow && testWindow.location !== undefined) {
                testWindow.close();
                return true;
            }
            return false;
        } catch (e) {
            return false;
        }
    }

    // 更准确的弹窗检测 - 实时检测
    function checkPopupSupportRealtime() {
        try {
            const testWindow = window.open('about:blank', '_blank', 'width=1,height=1,left=9999,top=9999');

            // 检查窗口是否真正打开
            if (testWindow && !testWindow.closed) {
                // 尝试访问窗口属性来确认窗口真正可用
                try {
                    testWindow.document;
                    testWindow.close();
                    return true;
                } catch (accessError) {
                    // 无法访问窗口内容，可能被阻止
                    if (!testWindow.closed) {
                        testWindow.close();
                    }
                    return false;
                }
            }
            return false;
        } catch (e) {
            console.log('弹窗检测异常:', e);
            return false;
        }
    }

    // 在页面加载时检测弹窗支持
    const popupSupported = checkPopupSupport();
    if (!popupSupported) {
        console.warn('⚠️ 浏览器可能阻止了弹窗，将使用页面跳转支付');
    }

    // 添加调试面板（仅在开发环境或localhost显示）
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        addDebugPanel();
    }

    function addDebugPanel() {
        const debugPanel = document.createElement('div');
        debugPanel.id = 'smartPaymentDebug';
        debugPanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
            max-height: 400px;
            overflow-y: auto;
            display: none;
        `;

        debugPanel.innerHTML = `
            <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                <strong>🔧 智能支付调试</strong>
                <button onclick="document.getElementById('smartPaymentDebug').style.display='none'"
                        style="background: #ff4d4f; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer; margin-left: 10px;">×</button>
            </div>
            <div id="debugContent">
                <div>初始弹窗检测: ${popupSupported ? '✅ 支持' : '❌ 不支持'}</div>
                <div>浏览器: ${navigator.userAgent.split(' ')[0]}</div>
                <div>时间: ${new Date().toLocaleString()}</div>
                <hr style="margin: 10px 0; border: 1px solid #333;">
                <div id="debugLogs"></div>
            </div>
        `;

        document.body.appendChild(debugPanel);

        // 添加显示/隐藏按钮
        const toggleButton = document.createElement('button');
        toggleButton.textContent = '🔧';
        toggleButton.style.cssText = `
            position: fixed;
            top: 10px;
            right: 320px;
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 50%;
            cursor: pointer;
            z-index: 10000;
            font-size: 16px;
        `;
        toggleButton.onclick = () => {
            const panel = document.getElementById('smartPaymentDebug');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        };
        document.body.appendChild(toggleButton);

        // 重写console.log来捕获智能支付相关日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);

            const message = args.join(' ');
            if (message.includes('智能支付') || message.includes('弹窗') || message.includes('备用') || message.includes('🔍') || message.includes('✅') || message.includes('❌')) {
                const debugLogs = document.getElementById('debugLogs');
                if (debugLogs) {
                    const logEntry = document.createElement('div');
                    logEntry.style.cssText = 'margin-bottom: 5px; padding: 2px 0; border-bottom: 1px solid #333;';
                    logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                    debugLogs.appendChild(logEntry);
                    debugLogs.scrollTop = debugLogs.scrollHeight;
                }
            }
        };
    }

    // --- Fetch API 兼容性检查和安全包装 ---
    if (typeof fetch === 'undefined') {
        console.error('Fetch API 不可用，请使用现代浏览器或添加 polyfill');
        showNotification('浏览器不支持，请使用现代浏览器', 'error');
        return;
    }

    // 创建安全的 fetch 包装函数
    window.safeFetch = async function(url, options = {}) {


        if (typeof fetch === 'undefined') {
            const error = new Error('浏览器不支持 fetch API，请使用现代浏览器');
            console.error('Fetch API 不可用:', error);
            throw error;
        }

        if (typeof window.fetch !== 'function') {
            const error = new Error('window.fetch 不是一个函数');
            console.error('window.fetch 类型错误:', typeof window.fetch);
            throw error;
        }

        try {
            const response = await fetch(url, options);
            return response;
        } catch (error) {
            console.error('Fetch 调用失败:', error);
            console.error('错误详情:', {
                name: error.name,
                message: error.message,
                stack: error.stack
            });
            throw error;
        }
    };

    // --- Initial Auth Check (No immediate redirect on page load) ---
    // The UI will be updated based on auth state, and content will be locked if not logged in.
    // API calls will handle redirects if token is missing/invalid.

    // DOM Elements
    const mainContainer = document.querySelector('main.container'); // Updated selector
    const loginBtn = document.getElementById('loginBtn');
    const userMenu = document.getElementById('userMenu');
    const navUsername = document.getElementById('navUsername');
    const userInitial = document.getElementById('userInitial');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const logoutBtn = document.getElementById('logoutBtn');
    const notificationContainer = document.getElementById('notificationContainer');
    const contactModal = document.getElementById('contactModal');
    const closeContactModal = document.getElementById('closeContactModal');

    // Contact Modal Functions
    async function showContactModal() {
        // 显示弹窗
        contactModal.style.display = 'flex';

        // 动态加载客服二维码
        await loadCustomerServiceQR();
    }

    function hideContactModal() {
        contactModal.style.display = 'none';
    }

    // 加载客服二维码
    async function loadCustomerServiceQR() {
        try {
            const API_BASE_URL = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
                ? window.location.protocol + '//' + window.location.hostname + ':3001/api'
                : '/api';

            console.log('🔍 加载客服二维码...');

            const response = await fetch(`${API_BASE_URL}/settings/customer-service-qr`);

            if (response.ok) {
                const data = await response.json();
                console.log('📞 客服信息:', data);

                if (data.success && data.qrCodeUrl) {
                    // 找到二维码容器
                    const qrCodeContainer = document.querySelector('.contact-modal-qrcode .qrcode-inner');

                    if (qrCodeContainer) {
                        // 替换为实际的二维码图片
                        qrCodeContainer.innerHTML = `
                            <img src="${data.qrCodeUrl}"
                                 alt="客服微信二维码"
                                 style="width: 150px; height: 150px; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                            <div style="display: none; padding: 20px; text-align: center; color: #666;">
                                二维码加载失败<br>
                                <small>请联系客服邮箱：<EMAIL></small>
                            </div>
                        `;
                        console.log('✅ 客服二维码加载成功');
                    }

                    // 更新其他联系信息
                    updateContactInfo(data);
                } else {
                    console.warn('⚠️ 客服二维码URL为空');
                    showFallbackContactInfo();
                }
            } else {
                console.error('❌ 获取客服信息失败:', response.status);
                showFallbackContactInfo();
            }
        } catch (error) {
            console.error('❌ 加载客服二维码失败:', error);
            showFallbackContactInfo();
        }
    }

    // 显示备用联系信息
    function showFallbackContactInfo() {
        const qrCodeContainer = document.querySelector('.contact-modal-qrcode .qrcode-inner');
        if (qrCodeContainer) {
            qrCodeContainer.innerHTML = `
                <div style="padding: 20px; text-align: center; color: #666; border: 2px dashed #ddd; border-radius: 10px;">
                    <div style="font-size: 24px; margin-bottom: 10px;">📞</div>
                    <div>客服二维码暂时无法显示</div>
                    <div style="font-size: 12px; margin-top: 5px;">请使用下方联系方式</div>
                </div>
            `;
        }
    }

    // 更新联系信息
    function updateContactInfo(data) {
        // 更新工作时间
        const timeInfo = document.querySelector('.contact-modal-section.time .info-desc');
        if (timeInfo && data.workingHours) {
            timeInfo.textContent = data.workingHours.replace('工作时间：', '');
        }

        // 可以在这里添加更多联系信息的更新
        console.log('📋 联系信息已更新');
    }

    // Contact Modal Event Listeners
    closeContactModal.addEventListener('click', hideContactModal);
    contactModal.addEventListener('click', (e) => {
        if (e.target === contactModal) {
            hideContactModal();
        }
    });

    // Make showContactModal available globally
    window.showContactModal = showContactModal;

    const inputText = document.getElementById('inputText');
    const outputText = document.getElementById('outputText');
    const submitBtn = document.getElementById('submitBtn');
    const optimizeType = document.getElementById('optimizeType');
    // const loadingIndicator = document.getElementById('loadingIndicator'); // Removed from HTML
    const uploadBtn = document.getElementById('uploadBtn');
    const documentUpload = document.getElementById('documentUpload');
    const documentInfo = document.getElementById('documentInfo');
    const fileNameSpan = document.getElementById('fileName');
    const wordCountSpan = document.getElementById('wordCount');
    const clearDocBtn = document.getElementById('clearDocBtn');

    // New DOM Elements for Tabs
    const tabButtons = document.querySelectorAll('.tab-button');
    const uploadTabContent = document.getElementById('uploadTabContent');
    const textTabContent = document.getElementById('textTabContent');

    let currentTab = 'upload'; // Default active tab

    // 创建switchToTab函数
    function switchToTab(targetTab) {
        console.log('切换到标签页:', targetTab);

        // 获取标签按钮和内容
        const tabButtons = document.querySelectorAll('.tab-button');
        const targetButton = document.querySelector(`[data-tab="${targetTab}"]`);
        const targetContent = document.getElementById(`${targetTab}TabContent`);

        if (targetButton && targetContent) {
            // 移除所有活动状态
            tabButtons.forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

            // 激活目标标签页
            targetButton.classList.add('active');
            targetContent.classList.add('active');
            currentTab = targetTab;

            console.log('✅ 成功切换到标签页:', targetTab);
        } else {
            console.error('❌ 找不到标签页元素:', targetTab);
        }
    }

    // 将函数暴露到全局作用域
    window.switchToTab = switchToTab;

    // New DOM Elements for Pricing and Payment
    const aiServiceCard = document.getElementById('aiServiceCard');
    const expertServiceCard = document.getElementById('expertServiceCard');
    const finalServicePrice = document.getElementById('finalServicePrice');
    const finalPriceDisplayDiv = document.querySelector('.final-price-display'); // Added this line
    const paymentButton = document.querySelector('.payment-section .primary-btn');
    const aiCalculatedPrice = document.getElementById('aiCalculatedPrice');
    const expertCalculatedPrice = document.getElementById('expertCalculatedPrice');

    // New DOM Elements for Payment Section
    const qrCodePlaceholder = document.querySelector('.payment-section .qr-code-placeholder');
    const qrCodeContainer = document.querySelector('.payment-section .qr-code-container');
    const qrCodeImage = document.querySelector('.qr-code-container img');
    const qrCodeAmount = document.getElementById('qrCodeAmount');
    const payNowAmount = document.getElementById('payNowAmount');
    const qrCodeLogoOverlay = document.querySelector('.qr-code-logo-overlay');
    const realtimeOrderIndicator = document.querySelector('.realtime-order-indicator');

    const paymentMethodButtons = document.querySelectorAll('.payment-method-btn');
    const payNowButton = document.querySelector('.pay-now-btn');

    // Payment Modal Elements
    const paymentModal = document.getElementById('paymentModal');
    const paymentModalClose = document.getElementById('paymentModalClose');
    const modalQrCodeImage = document.getElementById('modalQrCodeImage');
    const modalQrCodeLogoOverlay = document.getElementById('modalQrCodeLogoOverlay');
    const modalQrCodeAmount = document.getElementById('modalQrCodeAmount');
    const modalQrCodeService = document.getElementById('modalQrCodeService');


    // API Endpoints
    const API_BASE_URL = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
        ? window.location.protocol + '//' + window.location.hostname + ':3001/api'
        : '/api';
    const OPTIMIZE_API_ENDPOINT = `${API_BASE_URL}/optimize`;
    const PARAGRAPH_API_ENDPOINT = `${API_BASE_URL}/optimize-paragraph`;
    const UPLOAD_API_ENDPOINT = `${API_BASE_URL}/upload-document`;
    const TASK_STATUS_API_ENDPOINT = `${API_BASE_URL}/tasks/status`;

    // 全局变量已在文件开头声明

    // Pricing Variables
    const AI_PRICE_PER_THOUSAND_CHARS = 0.01;
    const EXPERT_PRICE_PER_THOUSAND_CHARS = 50;
    let selectedService = 'ai';
    let totalWordCount = 0;
    let countdownInterval;
    let timeLeft = 300; // 5 minutes in seconds
    let selectedPaymentMethod = 'alipay'; // Default payment method - 支付宝优先

    // 支付方式配置
    const PAYMENT_CONFIG = {
        alipay: {
            enabled: true,
            order: 1
        },
        wechat: {
            enabled_desktop: false,  // 桌面端隐藏
            enabled_mobile: true,    // 移动端显示
            maintenance_mobile: true, // 移动端维护状态
            order: 2
        },
        fuiou: {
            enabled: true,
            order: 3
        }
    };

    // 设备检测
    function isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth <= 768;
    }

    // 初始化支付方式显示
    function initializePaymentMethods() {
        const isMobile = isMobileDevice();

        // 处理主页面的支付方式按钮
        const wechatBtn = document.getElementById('wechatPaymentBtn');
        const wechatText = document.getElementById('wechatPaymentText');
        const fuiouBtn = document.getElementById('fuiouPaymentBtn');

        // 处理弹窗中的支付方式按钮
        const modalWechatBtn = document.getElementById('modalWechatPaymentBtn');
        const modalWechatText = document.getElementById('modalWechatPaymentText');
        const modalFuiouBtn = document.getElementById('modalFuiouPaymentBtn');

        if (isMobile) {
            // 移动端：显示微信支付但标注"待开放中"
            if (wechatBtn) {
                wechatBtn.style.display = 'block';
                wechatBtn.classList.add('maintenance');
                wechatBtn.disabled = true;
            }
            if (wechatText) {
                wechatText.textContent = '微信(待开放中)';
            }
            if (modalWechatBtn) {
                modalWechatBtn.style.display = 'block';
                modalWechatBtn.classList.add('maintenance');
                modalWechatBtn.disabled = true;
            }
            if (modalWechatText) {
                modalWechatText.textContent = '微信(待开放中)';
            }
        } else {
            // 桌面端：隐藏微信支付
            if (wechatBtn) {
                wechatBtn.style.display = 'none';
            }
            if (modalWechatBtn) {
                modalWechatBtn.style.display = 'none';
            }
        }

        // 支付宝在所有设备上都显示并可用
        const fuiouAlipayBtn = document.getElementById('fuiouAlipayPaymentBtn');
        const modalFuiouAlipayBtn = document.getElementById('modalFuiouAlipayPaymentBtn');
        if (fuiouAlipayBtn) {
            fuiouAlipayBtn.style.display = 'block';
            fuiouAlipayBtn.disabled = false;
            fuiouAlipayBtn.classList.remove('maintenance');
        }
        if (modalFuiouAlipayBtn) {
            modalFuiouAlipayBtn.style.display = 'block';
            modalFuiouAlipayBtn.disabled = false;
            modalFuiouAlipayBtn.classList.remove('maintenance');
        }

        // 微信在所有设备上都显示并可用
        const fuiouWechatBtn = document.getElementById('fuiouWechatPaymentBtn');
        const modalFuiouWechatBtn = document.getElementById('modalFuiouWechatPaymentBtn');
        if (fuiouWechatBtn) {
            fuiouWechatBtn.style.display = 'block';
            fuiouWechatBtn.disabled = false;
            fuiouWechatBtn.classList.remove('maintenance');
        }
        if (modalFuiouWechatBtn) {
            modalFuiouWechatBtn.style.display = 'block';
            modalFuiouWechatBtn.disabled = false;
            modalFuiouWechatBtn.classList.remove('maintenance');
        }

        console.log(`支付方式初始化完成 - 设备类型: ${isMobile ? '移动端' : '桌面端'}, 支付宝: 已启用, 微信: 已启用`);
    }

    // --- Email Validation ---
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // --- Service Agreement Functions ---
    function showServiceAgreement() {
        const agreementContent = `
            <h3>Writerpro服务条款协议</h3>
            <div style="max-height: 400px; overflow-y: auto; text-align: left; line-height: 1.6; font-size: 13px;">
                <div style="margin-bottom: 15px;">
                    <p><strong>欢迎您使用Writerpro服务！</strong></p>
                    <p>Writerpro的所有权和运作权归北京福强科技有限责任公司所有。北京福强科技有限责任公司同意按照《Writerpro服务条款》(下简称："本服务条款"或"本协议")规定以及不时发布的操作规则等提供基于互联网的相关服务。本服务主要面向成人年用户提供。请用户（以下称"用户"或"您"）仔细阅读本服务条款（特别是加粗部分），未成年人应在您的监护人陪同下阅读。除非您已接受本服务条款，否则您无权使用本条款下的相关服务。您的使用行为将视为统一接受本服务各项条款，包括北京福强科技有限责任公司对该服务条款随时所做的任何修改。</p>
                </div>

                <h4>1、基本服务条款</h4>
                <p>北京福强科技有限责任公司运用自己的操作系统通过国际互联网络为用户提供Writerpro服务。用户必须：</p>
                <p>（1）认真阅读并知晓Writerpro的产品介绍，遵守本服务条款和相关操作规则；</p>
                <p>（2）提供设备，包括个人电脑一台、连接网络设备；</p>
                <p>（3）个人上网和支付与此服务有关的费用。</p>
                <p>（4）遵守中华人民共和国相关法律法规（如果用户是中华人民共和国境外的使用者，还应遵守所属国家或地区的法律法规）。</p>

                <h4>2、软件自动升级功能</h4>
                <p>除非用户主动选择不使用Writerpro的自动升级功能，否则视为同意启动Writerpro默认的自动升级功能。</p>

                <h4>3、服务质量监测</h4>
                <p>为了监测Writerpro的服务质量，Writerpro在适当的时候会把软件运行时与服务质量有关的数据（包括但不仅限于查询延迟、北京福强科技有限责任公司提供服务的服务器地址等）发送到北京福强科技有限责任公司服务器。同时Writerpro将产生一个唯一编号，当您安装或升级Writerpro时，此编号和表示安装升级是否成功的消息将会发送到北京福强科技有限责任公司服务器。</p>

                <h4>4、在线浏览与购买服务</h4>
                <p><strong>4.1、您可以以游客模式浏览Writerpro服务页面，如您需要购买Writerpro产品/服务，您需要通过扫描支付宝/微信二维码进行相应付费后，Writerpro将为您提供相关服务。Writerpro服务属于虚拟交付服务，您在付费后，除非法律规定，Writerpro不接受任何形式的退款/重新提供服务等。包括但不限于:您对Writerpro服务效果不满意、认为服务效果不准确等。</strong></p>

                <p>4.2、单一功能的字符兑换码仅限在结算该功能时用以抵扣相应字符费用，不同功能兑换码之间无法相互抵扣，如您购买Writerpro相关兑换码，请注意选购您所需要具体功能的对应兑换码。请注意，字符兑换码将取整（1000字符的整倍数）消耗，兑换码一经消耗不设找零或退换，如结算不满1000字符时将取整消耗1000字符兑换码，请您根据字符消情况选择合适字符数量的兑换码。</p>

                <p><strong>4.3、用户在使用Writerpro时（包括但不限于您将文档等上传至Writerpro服务、您将服务内容进行分享、您发起的任何请求等）必须承诺遵守法律法规、社会主义制度、国家利益、公民合法权益、公共秩序、社会道德风尚和信息真实性等七条底线，不得在账号注册资料中出现违法和不良信息，不得存在如下情形，不得引导AI生成不良内容并进行传播，否则，我们有权采取封禁账号、不予退费（如有）、停止提供服务等措施，且由此产生的所有法律责任均由您自行承担：</strong></p>

                <div style="margin-left: 20px;">
                    <p>（1）反对宪法所确定的基本原则的；</p>
                    <p>（2）危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；</p>
                    <p>（3）损害国家荣誉和利益的；</p>
                    <p>（4）歪曲、丑化、亵渎、否定英雄烈士事迹和精神，以侮辱、诽谤或者其他方式侵害英雄烈士的姓名、肖像、名誉、荣誉的；</p>
                    <p>（5）宣扬恐怖主义、极端主义或者煽动实施恐怖活动、极端主义活动的；</p>
                    <p>（6）煽动民族仇恨、民族歧视，破坏民族团结的；</p>
                    <p>（7）破坏国家宗教政策，宣扬邪教和封建迷信的；</p>
                    <p>（8）散布谣言，扰乱经济秩序和社会秩序的；</p>
                    <p>（9）散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；</p>
                    <p>（10）侮辱或者诽谤他人，侵害他人名誉、隐私和其他合法权益的；</p>
                    <p>（11）危害网络安全、利用网络从事危害国家安全、荣誉和利益的；</p>
                    <p>（12）故意逃避技术审核发布不良信息，或引导AI生成不良信息，或予以传播；</p>
                    <p>（13）对他人进行暴力恐吓、威胁，实施人肉搜索的；</p>
                    <p>（14）涉及他人隐私、个人信息或资料的；</p>
                    <p>（15）散布污言秽语，损害社会公序良俗的；</p>
                    <p>（16）侵犯他人隐私权、名誉权、肖像权、知识产权等合法权益内容的；</p>
                    <p>（17）散布商业广告，或类似的商业招揽信息、过度营销信息及垃圾信息；</p>
                    <p>（18）侵害未成年人合法权益或者损害未成年人身心健康的；</p>
                    <p>（19）未获他人允许，偷拍、偷录他人，侵害他人合法权利的；</p>
                    <p>（20）包含虚假、有害、恐怖、暴力血腥、粗俗、侵害或其他道德上令人反感、影响他人身心健康内容的；</p>
                    <p>（21）进行学术作弊等学术不端行为；</p>
                    <p>（22）法律、行政法规禁止的其他内容。</p>
                </div>

                <p>根据相关法律、法规规定以及考虑到产品服务的重要性，用户同意：</p>
                <p>（1）在使用本服务时使用您本人的支付账号；</p>
                <p>（2）提供及时、详尽及准确的账户注册资料；</p>
                <p>（3）用户不得以营利、任何不正当手段或以违反诚实信用原则等为自己或他人开通本服务；</p>
                <p><strong>（4）用户充分了解并同意，您必须为自己注册帐号下的一切行为负责，包括但不限于您所上传/发表的任何内容以及由此产生的任何后果。您应对本服务中的内容自行加以判断，并承担因使用内容而引起的所有风险，包括因对内容的正确性、完整性或实用性的依赖而产生的风险。</strong></p>

                <h4>5、用户使用</h4>
                <p><strong>（1）请注意，本服务仅作为中立的AI技术，为您提供写作优化服务，真正有价值的是您独特的见解与深入的研究，让我们一起保护并鼓励原创。您需要同时遵守《Writerpro服务学术规范指引》。</strong></p>

                <p>（2）本服务仅供用户用于非商业用途。不得销售、转让、许可或以其它方式提供给任何第三方，如果要销售、转让或许可或以其它方式提供给Writerpro或者与Writerpro有关或派生的任何资料、服务或软件，则必须得到北京福强科技有限责任公司的书面允许。您不得利用Writerpro服务进行赠送、充值等任何经营行为，不得利用恶意软件、网络漏洞或其他违反诚实信用的非法方式影响我们的正常运营等。如您基于商业目的使用Writerpro服务的，您应当事先通过本平台联系北京福强科技有限责任公司并取得书面授权。</p>

                <p>（3）在本网站上，论文检测、比对以及提示功能的实现，并最终生成检测报告，这一系列流程均依托于本网站不断更新的数据库，经由大数据分析、云计算等技术手段处理后得出特定阶段的相应结果。用户明确知悉并同意，Writerpro服务内容及结果仅能作为用户的参考资料，您应对本服务内容自行判断分析。</p>

                <p>如果用户对Writerpro有任何意见，或有如何改进的建议，可向北京福强科技有限责任公司提出。请注意，如果这样做，还会授予北京福强科技有限责任公司和第三方在Writerpro（或第三方软件）中无偿使用和加入用户的建议或意见的权利。北京福强科技有限责任公司保留在Writerpro中投放商业性广告的权利。</p>

                <p><strong>（4）数据保留： 除非法律另有规定，您上传的文档以及通过Writerpro服务生成的内容，将在您完成服务后的3天内被存储在我们的服务器上，以方便您后续查看和管理。超出此期限，我们可能会定期清理存储数据。您可以随时通过服务界面或联系客服要求删除您的历史数据。</strong></p>

                <h4>6、知识产权</h4>
                <p>（1）用户承认北京福强科技有限责任公司拥有对Writerpro的所有权利，包括但不限于所有知识产权。"知识产权"指在专利法、著作权法、商标法、反不正当竞争法等法中规定的所有权利以及其中的所有应用、更新、扩展和恢复，无论在现在还是以后在全球范围内实施和生效。用户同意不会修改、改编、翻译Writerpro、创作Writerpro的派生作品、通过反编译、反向工程、反汇编或其它方式从Writerpro得到源代码或所有数据。</p>

                <p>（2）本服务可能内含让用户上传用户原创内容/用户被授权内容。北京福强科技有限责任公司尊重和鼓励用户创作的内容。用户在Writerpro上上传的内容，著作权均归作者本人所有。用户可授权第三方以任何方式使用，不需要得到北京福强科技有限责任公司的同意。</p>

                <p>（3）北京福强科技有限责任公司有权对用户发布的内容进行机器审核，但对用户发表的任何内容均不承担任何明示或默示的保证，均不代表北京福强科技有限责任公司观点或意见。北京福强科技有限责任公司有权根据相关证据结合《侵权责任法》、《信息网络传播权保护条例》等法律法规及本服务条款的规定对侵权信息进行处理。</p>

                <h4>7、用户隐私制度</h4>
                <p>尊重用户隐私是北京福强科技有限责任公司的一项基本政策。北京福强科技有限责任公司将按照网站上公布的《Writerpro隐私政策》收集、存储、使用、披露和保护您的个人信息，请您完整阅读上述隐私权政策，以帮助您更好地保护您的个人信息。</p>

                <h4>8、年龄限制</h4>
                <p><strong>您确认，您应当具备中华人民共和国法律规定的与您行为相适应的民事行为能力，确保有能力对您使用北京福强科技有限责任公司提供服务的一切行为独立承担责任。若您不具备前述主体资格或您是未满十八周岁的未成年人，请在您的监护人的陪同下阅读本服务条款，并在取得他们对您使用服务的行为，以及对本服务条款的同意之后，使用本服务；北京福强科技有限责任公司在依据法律规定或本协议约定要求您承担责任时，有权向您的监护人追偿。</strong></p>

                <h4>9、服务变更</h4>
                <p>北京福强科技有限责任公司保留随时变更、中断或终止服务而不需通知用户的权利。用户接受北京福强科技有限责任公司行使变更、中断或终止服务的权利，北京福强科技有限责任公司不需对用户或第三方负责。</p>

                <h4>10、不可抗力条款</h4>
                <p>北京福强科技有限责任公司对不可抗力导致的损失不承担责任。本服务条款所指不可抗力包括：天灾、法律法规或政府指令的变更，因技术局限性、网络服务特性而特有的原因，例如境内外基础电信运营商的故障、计算机或互联网相关技术缺陷、互联网覆盖范围限制、计算机病毒、黑客攻击等因素，及其他合法范围内的不能预见、不能避免并不能克服的客观情况。</p>

                <h4>11、通知</h4>
                <p>所有发给用户的通知都可通过用户电子邮件、站内通知或在网站显著位置公告的方式进行传送。北京福强科技有限责任公司将通过上述方法之一将消息传递给用户，告知其服务条款的修改、服务变更、或其它重要事情。同时，北京福强科技有限责任公司保留在Writerpro中投放商业性广告的权利以及利用用户登录的邮箱发布商业性广告的权利，包括但不限于在网站登录页面及登录后任何页面内放置商业广告、向用户发送商业性广告邮件以及在用户发出的电子邮件内附上商业性广告及／或广告链接等形式，但用户有权在邮箱内的广告邮件内选择拒绝接受此类广告等信息。</p>

                <h4>12、违约责任</h4>
                <p><strong>若用户的行为违反了国家法律法规的规定、本服务条款的约定或者违反Writerpro网站上的规则，北京福强科技有限责任公司有权视用户的行为性质，对用户采取包括但不限于暂停或终止部分或全部本服务，限制、中止或终止用户账号的登录和使用、追究法律责任等措施，也无需向用户退还支付的任何费用。用户若有违反本服务条款任何行为，导致任何第三方损害的，用户应当独立承担责任；北京福强科技有限责任公司因此遭受损失的，用户也应当一并赔偿。用户同意保障和维护北京福强科技有限责任公司的利益，负责支付由用户违反本服务条款和/或其他服务条款引起的律师费用、损害补偿费用、政府机关处罚费用和其它侵权赔偿费用等。</strong></p>

                <h4>13、改进服务</h4>
                <p><strong>我们会以合理的技术和谨慎的态度提供Writerpro服务。但同时请您知悉，Writerpro是中立技术提供方，基于您的请求为您提供机器优化等服务，除非法律要求，基于技术限制性和中立性，Writerpro无法对提供的服务作出正确性、可靠性、可用性保证。如您向第三方机构提交Writerpro服务成果后，未能得到正面的效果评价，在此过程中出现的任何后果，均由用户自行承担，Writerpro不承担支持退费/赔偿等责任。</strong></p>

                <h4>14、法律</h4>
                <p>本协议适用中华人民共和国的法律（不含冲突法）。当本协议的任何内容与中华人民共和国法律相抵触时，应当以法律规定为准，同时相关内容将按法律规定进行修改或重新解释，而本协议其他部分的法律效力不变。</p>
                <p>凡因本协议引起的或与本协议有关的任何争议,双方应协商解决。协商不成时，任何一方均可向被告所在地有管辖权的人民法院提起诉讼。</p>
                <p>上述条款是各方针对本文主题的全部协议，优先于并取代所有以前或同期对此类主题的书面或口头的规定或协议。对上述任何条款的排除必须采取书面形式，并得到北京福强科技有限责任公司和/或Writerpro一同提供或通过Writerpro提供其软件的第三方书面签字，才能生效。</p>

                <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-left: 4px solid #007bff; font-size: 12px;">
                    <h5 style="margin-top: 0; color: #007bff;">学术规范指引</h5>
                    <p><strong>学术探索与知识传播的道路上，坚守学术诚信是每一位研究者、创作者的基本准则。</strong>Writerpro致力于为您提供专业的降重辅助服务，助力您优化学术表达，同时更期望您能遵循严格的学术规范，产出高质量、原创性的学术成果。</p>
                    <p><strong>关于原创性：</strong>学术研究的基石是原创思想与独特发现。请确保您输入至Writerpro进行优化的内容，必须基于您本人实质性的研究工作，杜绝抄袭、剽窃他人已发表或未发表作品的行为。</p>
                    <p><strong>正确使用方式：</strong>本服务旨在辅助您解决因语言习惯、写作技巧等因素导致的文本重复问题，它不能也不应被用于掩盖学术不端行为。降重后的文本仅作为参考建议，您务必进行严格的人工审核。</p>
                </div>

                <p style="margin-top: 20px; font-size: 12px; color: #666; text-align: center;">
                    如用户对本服务条款内容有任何疑问，您可以通过官网底部"联系客服"获取相关信息与帮助。<br>
                    <strong>北京福强科技有限责任公司</strong><br>
                    最后更新：2025年3月
                </p>
            </div>
        `;
        showCustomModal('Writerpro服务条款协议', agreementContent);
    }

    function showPrivacyPolicy() {
        const privacyContent = `
            <h3>Writerpro隐私政策协议</h3>
            <div style="max-height: 400px; overflow-y: auto; text-align: left; line-height: 1.6; font-size: 13px;">
                <div style="margin-bottom: 15px;">
                    <p><strong>欢迎您使用Writerpro服务（以下简称"Writerpro服务"）！</strong></p>
                    <p>Writerpro服务提供者为北京福强科技有限责任公司。我们深知个人信息对您的重要性，并承诺保护您的个人信息及隐私安全。您在使用Writerpro服务时，我们将按照本政策收集和使用您的相关个人信息。在您开始使用Writerpro服务，请您务必先仔细阅读和理解本政策，特别是加粗条款，确保您充分理解和同意后再开始使用。</p>
                    <p><strong>【特别提示】请您在使用Writerpro服务前，仔细阅读（未成年人请在监护人陪同下阅读）并了解本政策（特别是加粗或下划线标注的内容），如您是未满14周岁的儿童，还应请您在监护人仔细阅读并同意本协议。一旦您使用或在我们更新本政策后继续使用我们的产品或服务，即意味着您同意本政策并同意我们按照本政策处理您的相关个人信息。</strong></p>
                </div>

                <h4>一、我们如何收集和使用您的个人信息</h4>
                <p>我们会出于以下目的，收集和使用您以下类型的个人信息：</p>

                <p><strong>1、购买使用Writerpro服务</strong></p>
                <p>您在购买使用Writerpro服务时，您需要通过扫描支付宝/微信二维码进行相应付费，我们将为您付费行为生成【交易单号】。</p>

                <p><strong>2、客服与售后</strong></p>
                <p>依照相关法律法规规定及监管要求，或当您与我们联系时，我们的客服或售后可能会需要您提供身份信息和交易信息以核验您的身份，以便帮助您问题，或记录相关问题的处理方案及结果。</p>
                <p><strong>请注意，您的身份信息属于个人敏感信息，请您谨慎提供，如果拒绝提供您将可能无法获得相关服务，但不影响北京福强科技有限责任公司的其他功能与服务的正常使用。</strong></p>

                <p><strong>3、维护基础功能的正常运行</strong></p>
                <p>在您使用我们服务过程中，为识别账号异常状态、了解产品适配性，维护基础功能的正常运行，我们可能会自动收集、储存关于您使用的服务以及使用方式的信息并将这些信息进行关联，这些信息包括：</p>
                <p><strong>日志信息：</strong>当您使用我们的服务时，我们可能会自动收集您对我们服务的详细使用情况，作为有关网络日志保存。日志信息包括您的搜索查询内容、粘贴板信息、IP地址、浏览器的类型、网络环境、访问日期和时间及您访问的崩溃记录、停留时长、刷新记录。</p>
                <p>请注意，单独的设备信息、日志信息是无法识别特定自然人身份的个人信息。如果我们将这类非个人信息与其他个人信息结合用于识别特定自然人身份，或者将其与个人信息结合使用，则在结合使用期间，这类非个人信息将被视为个人信息，除取得您授权或法律法规另有规定外，我们会将该类个人信息做匿名化、去标识化处理。</p>

                <p><strong>4、为您提供安全保障</strong></p>
                <p>为提高您使用我们及合作伙伴提供服务的安全性，保护您或其他用户或公众的人身财产安全免遭侵害，更好地预防钓鱼网站、欺诈、网络漏洞、计算机病毒、网络攻击、网络侵入等安全风险，更准确地识别违反法律法规或Writerpro服务相关协议规则的情况，我们可能会收集、使用或整合您的账户信息、交易信息、设备信息、日志信息以及我们关联公司、合作伙伴取得您授权或依据法律共享的个人信息，来综合判断您账户及交易风险、进行身份验证、检测及防范安全事件，并依法采取必要的记录、审计、分析、处置措施。</p>

                <p><strong>5、迭代升级</strong></p>
                <p>我们希望提供给您的产品和服务是完善的，所以我们会不断改进我们的产品和服务，我们可能将通过Writerpro服务所收集的个人信息，来诊断系统问题，优化产品体验；我们可能让您参与有关Writerpro服务的调查，帮助我们改善现有服务或设计新服务。</p>

                <p><strong>6、向您提供商品或服务的信息展示和推送</strong></p>
                <p>基于您向我们提供的信息、我们可能收集的信息及我们通过间接方式收集到的您的信息，我们可能会基于上述一项或几项信息的结合，进行推荐算法建模、程序化广告推荐算法建模、用户行为分析及用户画像，用于提取您的浏览、搜索偏好、行为习惯相关特征，以便向您提供更契合您需求的页面展示相关内容。</p>

                <p style="margin-top: 20px; font-size: 12px; color: #666; text-align: center;">
                    <strong>北京福强科技有限责任公司</strong><br>
                    最后更新：2025年3月
                </p>
            </div>
        `;
        showCustomModal('Writerpro隐私政策协议', privacyContent);
    }

    function showCustomModal(title, content) {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.5); z-index: 10000; display: flex;
            align-items: center; justify-content: center;
        `;

        modal.innerHTML = `
            <div style="background: white; border-radius: 12px; max-width: 600px; width: 90%; max-height: 80%; padding: 0; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                <div style="padding: 20px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                    <h3 style="margin: 0; color: #333;">${title}</h3>
                    <button onclick="this.closest('.custom-modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">&times;</button>
                </div>
                <div style="padding: 20px;">
                    ${content}
                </div>
                <div style="padding: 20px; border-top: 1px solid #eee; text-align: right;">
                    <button onclick="this.closest('.custom-modal').remove()" style="background: #8A2BE2; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">确定</button>
                </div>
            </div>
        `;

        modal.className = 'custom-modal';
        document.body.appendChild(modal);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    // --- Save Email to Temp User ---
    async function saveEmailToTempUser(email) {
        const headers = getAuthHeaders();
        const response = await fetch(`${API_BASE_URL}/users/temp/save-email`, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify({ email: email })
        });

        if (!response.ok) {
            throw new Error('保存邮箱失败');
        }

        return await response.json();
    }

    // 将函数暴露到全局作用域
    window.showServiceAgreement = showServiceAgreement;
    window.showPrivacyPolicy = showPrivacyPolicy;

    // --- Notification System ---
    function showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.classList.add('notification', type);
        notification.innerHTML = `
            <span class="notification-icon">${getNotificationIcon(type)}</span>
            <span>${message}</span>
        `;
        notificationContainer.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, duration);
    }

    function getNotificationIcon(type) {
        switch (type) {
            case 'success': return '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-8.93"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>';
            case 'error': return '❌';
            case 'info': return 'ℹ️';
            default: return '💬';
        }
    }

    // --- Payment Modal Logic ---
    function openPaymentModal(serviceType) {
        console.log(`🔄 打开支付弹窗 (${serviceType})，完全重置到最初状态`);

        // 1. 清除所有缓存
        qrCodeCache.alipay = null;
        qrCodeCache.wechat = null;

        // 2. 清除倒计时
        clearInterval(countdownInterval);

        // 3. 重置协议复选框为未勾选状态
        const agreementCheckbox = document.getElementById('agreementCheckbox');
        if (agreementCheckbox) {
            agreementCheckbox.checked = false;
        }

        // 4. 隐藏协议警告文字
        const agreementWarning = document.querySelector('.agreement-warning');
        if (agreementWarning) {
            agreementWarning.style.display = 'none';
        }

        // 5. 重置支付按钮状态（取消所有按钮的选中状态）
        const alipayBtn = document.getElementById('alipayDirectBtn');
        const wechatBtn = document.getElementById('wechatDirectBtn');

        if (alipayBtn) {
            alipayBtn.classList.remove('selected', 'active');
            // 重置为支付宝默认蓝色
            alipayBtn.style.backgroundColor = '#00A0E9';
            alipayBtn.style.color = 'white';
            alipayBtn.style.display = 'none'; // 隐藏按钮，用户需要重新选择
        }

        if (wechatBtn) {
            wechatBtn.classList.remove('selected', 'active');
            // 重置为微信默认绿色
            wechatBtn.style.backgroundColor = '#07C160';
            wechatBtn.style.color = 'white';
            wechatBtn.style.display = 'none'; // 隐藏按钮，用户需要重新选择
        }

        // 6. 重置为原始状态：显示占位符，隐藏所有二维码容器
        clearQRCodeContainers();

        currentServiceType = serviceType;

        selectedService = serviceType;
        updatePriceDisplay(); // Update prices before showing modal

        // 保存选择的服务类型
        localStorage.setItem('lastSelectedService', serviceType);

        const price = calculatePrice(totalWordCount, serviceType);
        modalQrCodeAmount.textContent = price;
        modalQrCodeService.textContent = serviceType === 'ai' ? 'WriterPro内容优化' : '专家润色服务';

        // 检查用户登录状态并控制邮箱收集区域显示
        const isLoggedIn = checkLoginStatus();
        const emailCollectionDiv = document.querySelector('.email-collection');

        if (emailCollectionDiv) {
            if (isLoggedIn) {
                // 已登录用户：隐藏邮箱收集区域
                emailCollectionDiv.style.display = 'none';
                console.log('👤 已登录用户，隐藏邮箱收集区域');
            } else {
                // 未登录用户：显示邮箱收集区域
                emailCollectionDiv.style.display = 'block';
                console.log('👥 未登录用户，显示邮箱收集区域');
            }
        }

        // 创建一个简单的二维码占位符
        const canvas = document.createElement('canvas');
        canvas.width = 150;
        canvas.height = 150;
        const ctx = canvas.getContext('2d');
        
        // 绘制白色背景
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, 150, 150);
        
        // 绘制黑色边框
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 5;
        ctx.strokeRect(10, 10, 130, 130);
        
        // 绘制一些模拟的二维码图案
        ctx.fillStyle = 'black';
        for (let i = 0; i < 5; i++) {
            for (let j = 0; j < 5; j++) {
                if (Math.random() > 0.5) {
                    ctx.fillRect(30 + i * 20, 30 + j * 20, 15, 15);
                }
            }
        }
        
        // 在中间绘制服务类型
        ctx.fillStyle = 'white';
        ctx.fillRect(60, 60, 30, 30);
        ctx.fillStyle = '#FF6B35';
        ctx.font = '20px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(serviceType === 'ai' ? 'AI' : 'PRO', 75, 75);
        
        // 将Canvas转换为图片URL
        modalQrCodeImage.src = canvas.toDataURL('image/png');

        updateModalQrCodeLogo();
        // 不在这里启动倒计时，等用户选择支付方式后再启动

        // 确保显示占位符，隐藏所有二维码容器
        const alipayContainer = document.getElementById('alipayQrContainer');
        const wechatContainer = document.getElementById('wechatQrContainer');
        // 确保选择支付弹窗内的占位符
        const placeholder = document.querySelector('#paymentModal .qr-code-placeholder');

        if (placeholder) placeholder.style.display = 'block';
        if (alipayContainer) alipayContainer.style.display = 'none';
        if (wechatContainer) wechatContainer.style.display = 'none';

        paymentModal.style.display = 'flex';
        
        // 更新支付按钮
        const alipayDirectBtn = document.getElementById('alipayDirectBtn');
        const wechatDirectBtn = document.getElementById('wechatDirectBtn');

        // 更新金额
        document.querySelectorAll('.alipay-amount').forEach(el => el.textContent = price);
        document.querySelectorAll('.wechat-amount').forEach(el => el.textContent = price);

        // 根据当前选择的支付方式显示或隐藏支付按钮
        if (selectedPaymentMethod === 'alipay') {
            if (alipayDirectBtn) alipayDirectBtn.style.display = 'block';
            if (wechatDirectBtn) wechatDirectBtn.style.display = 'none';
        } else if (selectedPaymentMethod === 'wechat') {
            if (alipayDirectBtn) alipayDirectBtn.style.display = 'none';
            if (wechatDirectBtn) wechatDirectBtn.style.display = 'block';
        } else if (selectedPaymentMethod === 'fuiou-alipay' || selectedPaymentMethod === 'fuiou-wechat') {
            // 富有支付使用二维码显示，隐藏其他按钮
            if (alipayDirectBtn) alipayDirectBtn.style.display = 'none';
            if (wechatDirectBtn) wechatDirectBtn.style.display = 'none';
        } else {
            // 默认显示微信支付按钮（因为微信支付是默认选中的）
            if (alipayDirectBtn) alipayDirectBtn.style.display = 'none';
            if (wechatDirectBtn) wechatDirectBtn.style.display = 'block';
        }
        

    }

    function closePaymentModal() {
        clearInterval(countdownInterval);
        console.log('🔄 关闭支付弹窗');
        paymentModal.style.display = 'none';
    }

    if (paymentModalClose) {
        paymentModalClose.addEventListener('click', closePaymentModal);
    }

    // --- Payment Countdown Logic (Modal) ---
    function startModalCountdown() {
        clearInterval(countdownInterval); // Clear any existing interval
        timeLeft = 300; // Reset to 5 minutes

        countdownInterval = setInterval(() => {
            const minutes = Math.floor(timeLeft / 60);
            let seconds = timeLeft % 60;
            seconds = seconds < 10 ? '0' + seconds : seconds;
            const timeString = `${minutes}:${seconds}`;

            // 更新当前可见容器的倒计时元素
            const alipayContainer = document.getElementById('alipayQrContainer');
            const wechatContainer = document.getElementById('wechatQrContainer');

            if (alipayContainer && alipayContainer.style.display === 'block') {
                const alipayCountdown = document.getElementById('alipayCountdownTime');
                if (alipayCountdown) alipayCountdown.textContent = timeString;
            }

            if (wechatContainer && wechatContainer.style.display === 'block') {
                const wechatCountdown = document.getElementById('wechatCountdownTime');
                if (wechatCountdown) wechatCountdown.textContent = timeString;
            }

            if (timeLeft <= 0) {
                clearInterval(countdownInterval);
                showNotification('支付已超时，请重新下单', 'error');
                closePaymentModal();
            } else {
                timeLeft--;
            }
        }, 1000);
    }

    function updateModalQrCodeLogo() {
        modalQrCodeLogoOverlay.innerHTML = `
            ${selectedPaymentMethod === 'wechat' ?
                `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"
                        stroke="#07C160" stroke-width="2"/>
                </svg>` :
                selectedPaymentMethod === 'fuiou-alipay' ?
                `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" fill="#1677FF"/>
                  <path d="M8 9h8v2H8V9zm0 4h8v2H8v-2z" fill="#fff"/>
                </svg>` :
                selectedPaymentMethod === 'fuiou-wechat' ?
                `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" fill="#07C160"/>
                  <path d="M8 9h8v2H8V9zm0 4h8v2H8v-2z" fill="#fff"/>
                </svg>` :
                `<svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z" fill="#00A0E9"/>
                  <path d="M15.5 8.5h-7c-.552 0-1 .448-1 1v5c0 .552.448 1 1 1h7c.552 0 1-.448 1-1v-5c0-.552-.448-1-1-1z" fill="#fff"/>
                  <path d="M12 10.5c-.828 0-1.5.672-1.5 1.5s.672 1.5 1.5 1.5 1.5-.672 1.5-1.5-.672-1.5-1.5-1.5z" fill="#00A0E9"/>
                </svg>`
            }
        `;
    }

    // --- UI State Logic ---
    function setOptimizationState(isOptimizing) {
        if (isOptimizing) {
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = '优化处理中...';
            }
            if (uploadBtn) uploadBtn.disabled = true;
            if (clearDocBtn) clearDocBtn.disabled = true;
            if (inputText) inputText.disabled = true;
            if (payNowButton) payNowButton.disabled = true; // Disable payment button during optimization
        } else {
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.textContent = '开始优化';
            }
            if (uploadBtn) uploadBtn.disabled = false;
            if (clearDocBtn) clearDocBtn.disabled = false;
            if (inputText) inputText.disabled = false;
            // payNowButton.disabled = false; // Enable payment button after optimization - handled by updatePriceDisplay
        }
    }

    // --- Auth Logic ---
    const checkLoginStatus = () => {
        return window.AuthManager.isLoggedIn();
    };

    const requireLogin = (actionName = '此操作') => {
        if (!checkLoginStatus()) {
            showLoginPrompt(actionName);
            return false;
        }
        return true;
    };

    const showLoginPrompt = (actionName) => {
        // 简单直接的提示方式
        const userConfirm = confirm(`请先登录后再${actionName}。\n\n点击"确定"跳转到登录页面，点击"取消"继续浏览。`);
        if (userConfirm) {
            window.location.href = 'login.html';
        }
    };

    const updateUIBasedOnAuthState = async () => {
        const isLoggedIn = window.AuthManager.isLoggedIn();

        if (isLoggedIn) {
            if (loginBtn) loginBtn.style.display = 'none';
            if (userMenu) userMenu.style.display = 'block';
            if (mainContainer) mainContainer.classList.remove('content-locked');

            // 获取用户信息并更新导航栏
            await updateNavUserInfo();
        } else {
            if (loginBtn) loginBtn.style.display = 'block';
            if (userMenu) userMenu.style.display = 'none';
            if (mainContainer) mainContainer.classList.add('content-locked');
        }
    };

    // 更新导航栏用户信息
    const updateNavUserInfo = async () => {
        const headers = getAuthHeaders();
        if (!headers) return;

        try {
            const response = await safeFetch(`${API_BASE_URL}/auth/me`, {
                method: 'GET',
                headers: headers,
            });

            if (response.ok) {
                const user = await response.json();
                if (navUsername) navUsername.textContent = user.username;
                if (userInitial) userInitial.textContent = user.username.charAt(0).toUpperCase();
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
        }
    };

    // 修改密码弹窗逻辑
    const passwordModal = document.getElementById('passwordModal');
    const closePasswordModalBtn = document.getElementById('closePasswordModal');
    const cancelPasswordBtn = document.getElementById('cancelPasswordBtn');
    const changePasswordForm = document.getElementById('changePasswordForm');

    const openPasswordModal = () => {
        if (passwordModal) passwordModal.style.display = 'flex';
        clearPasswordErrors();
        if (changePasswordForm) changePasswordForm.reset();
    };

    const closePasswordModal = () => {
        if (passwordModal) passwordModal.style.display = 'none';
        clearPasswordErrors();
        if (changePasswordForm) changePasswordForm.reset();
    };

    const clearPasswordErrors = () => {
        const errorElements = ['currentPasswordError', 'newPasswordError', 'confirmNewPasswordError'];
        errorElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.textContent = '';
        });
    };

    if (changePasswordBtn) {
        changePasswordBtn.addEventListener('click', (e) => {
            e.preventDefault();
            openPasswordModal();
        });
    }

    if (closePasswordModalBtn) {
        closePasswordModalBtn.addEventListener('click', closePasswordModal);
    }

    if (cancelPasswordBtn) {
        cancelPasswordBtn.addEventListener('click', closePasswordModal);
    }

    if (passwordModal) {
        passwordModal.addEventListener('click', (e) => {
            if (e.target === passwordModal) {
                closePasswordModal();
            }
        });
    }

    // 修改密码表单提交
    if (changePasswordForm) {
        changePasswordForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            clearPasswordErrors();

            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmNewPassword = document.getElementById('confirmNewPassword').value;

            if (newPassword !== confirmNewPassword) {
                const confirmError = document.getElementById('confirmNewPasswordError');
                if (confirmError) confirmError.textContent = '两次输入的新密码不一致';
                showNotification('两次输入的新密码不一致', 'error');
                return;
            }

            if (newPassword.length < 6) {
                const newPasswordError = document.getElementById('newPasswordError');
                if (newPasswordError) newPasswordError.textContent = '新密码长度至少为6个字符';
                showNotification('新密码长度至少为6个字符', 'error');
                return;
            }

            const headers = getAuthHeaders();
            if (!headers) {
                showNotification('请先登录', 'error');
                return;
            }

            try {
                const response = await safeFetch(`${API_BASE_URL}/auth/password`, {
                    method: 'PUT',
                    headers: headers,
                    body: JSON.stringify({ currentPassword, newPassword, confirmNewPassword }),
                });

                const data = await response.json();

                if (!response.ok) {
                    if (data.errors && Array.isArray(data.errors)) {
                        data.errors.forEach(err => {
                            const errorElement = document.getElementById(err.param + 'Error');
                            if (errorElement) errorElement.textContent = err.msg;
                            showNotification(err.msg, 'error');
                        });
                    } else if (data.msg) {
                        showNotification(data.msg, 'error');
                    } else {
                        showNotification('修改密码失败', 'error');
                    }
                    return;
                }

                showNotification(data.msg, 'success');
                closePasswordModal();
            } catch (error) {
                showNotification('网络或服务器错误，请稍后再试', 'error');
                console.error('Error changing password:', error);
            }
        });
    }

    if (logoutBtn) {
        logoutBtn.addEventListener('click', async (e) => {
            e.preventDefault();
            window.AuthManager.clearToken();
            await updateUIBasedOnAuthState();
            // 删除：不再显示退出登录提示（页面变化已说明）
        });
    }

    const getAuthHeaders = (isFormData = false) => {
        const token = window.AuthManager.getToken();

        if (token) {
            // 验证token有效性
            if (!window.AuthManager.validateToken(token)) {
                console.warn('Token无效或已过期，清除token');
                window.AuthManager.clearToken();
                // 继续使用临时用户模式
            } else {
                // 用户已登录，返回正常的认证头
                if (isFormData) {
                    return { 'Authorization': `Bearer ${token}` };
                }
                return {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                };
            }
        }

        // 用户未登录或token无效，使用临时用户模式
        console.log('🔄 使用临时用户模式');

        // 获取或生成临时用户ID
        const tempUserId = window.TempUserManager ? window.TempUserManager.getTempUserId() :
            'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        if (isFormData) {
            return {
                'X-Temp-User-Id': tempUserId
            };
        }

        return {
            'X-Temp-User-Id': tempUserId,
            'Content-Type': 'application/json'
        };
    };

    // 安全的fetch函数，自动处理token过期
    const safeFetch = async (url, options = {}) => {
        try {
            const response = await fetch(url, options);

            // 如果收到401错误，说明token可能过期
            if (response.status === 401) {
                console.log('收到401错误，token可能过期，清除token');
                window.AuthManager.clearToken();

                // 更新UI状态
                updateUIBasedOnAuthState().catch(console.error);

                // 如果是在需要登录的页面，跳转到登录页
                if (window.location.pathname !== '/login.html' && window.location.pathname !== '/') {
                    showNotification('登录已过期，请重新登录', 'warning');
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);
                }
            }

            return response;
        } catch (error) {
            console.error('网络请求失败:', error);
            throw error;
        }
    };

    // --- 自动保存优化结果到历史记录 ---
    async function saveOptimizedDocument(content, originalFileName) {
        try {
            // 使用统一的认证头获取函数
            const headers = getAuthHeaders();
            debugLog('🔐 自动保存使用认证头:', Object.keys(headers), 'debug');
            debugLog('自动保存优化结果到历史记录...', 'debug');
            
            const response = await fetch(`${API_BASE_URL}/documents/save-optimized`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({ 
                    content: content || '无内容', 
                    fileName: originalFileName || '未命名文档',
                    documentId: currentDocument ? currentDocument.id : null,
                    skipDownload: true // 标记不需要下载
                })
            });

            debugLog('响应状态:', response.status, 'debug');
            
            if (!response.ok) {
                const errorText = await response.text();
                console.error('保存失败响应:', errorText);
                try {
                    const errorData = JSON.parse(errorText);
                    throw new Error(errorData.message || '保存优化文档失败');
                } catch (e) {
                    throw new Error(`保存失败 (${response.status}): ${errorText || '未知错误'}`);
                }
            }

            // 只解析响应，不下载文件
            const result = await response.json();
            console.log('保存成功:', result);
            // 删除：不再显示自动保存提示
            return true;
        }
        catch (error) {
            console.error('保存优化文档失败:', error);
            showNotification('保存优化文档失败: ' + error.message, 'error');
            return false;
        }
    }
    
    // --- 测试自动保存功能 ---
    window.testAutoSave = async function() {
        if (!currentDocument) {
            console.error('没有当前文档，无法测试自动保存');
            showNotification('没有当前文档，请先上传文档', 'error');
            return;
        }
        
        try {
            console.log('测试自动保存功能...');
            const testContent = '这是一个测试内容，用于验证自动保存功能。' + Date.now();
            
            // 调用自动保存函数
            await saveOptimizedDocument(testContent, currentDocument.name);
            
            // 检查保存结果
            const debugResponse = await fetch(`${API_BASE_URL}/documents/debug`, {
                headers: getAuthHeaders()
            });
            
            if (debugResponse.ok) {
                const debugData = await debugResponse.json();
                console.log('调试数据:', debugData);
                showNotification(`测试完成，发现 ${debugData.count} 个文档`, 'success');
            } else {
                throw new Error('获取调试数据失败');
            }
        } catch (error) {
            console.error('测试自动保存失败:', error);
            showNotification('测试自动保存失败: ' + error.message, 'error');
        }
    };

    // --- Pricing Logic ---
    function calculatePrice(wordCount, serviceType) {
        const charsPerThousand = Math.ceil(wordCount / 1000);
        if (serviceType === 'ai') {
            return (charsPerThousand * AI_PRICE_PER_THOUSAND_CHARS).toFixed(2);
        } else if (serviceType === 'expert') {
            return (charsPerThousand * EXPERT_PRICE_PER_THOUSAND_CHARS).toFixed(2);
        }
        return '0.00';
    }

    function updatePriceDisplay() {
        const aiPrice = calculatePrice(totalWordCount, 'ai');
        const expertPrice = calculatePrice(totalWordCount, 'expert');

        const aiCalculatedPrice = document.getElementById('aiCalculatedPrice');
        const expertCalculatedPrice = document.getElementById('expertCalculatedPrice');
        const aiPriceDisplay = document.getElementById('aiPriceDisplay');
        const expertPriceDisplay = document.getElementById('expertPriceDisplay');

        // New: Get elements for the payment modal's final price display
        const modalFinalServicePrice = document.querySelector('#paymentModal #finalServicePrice');
        const modalPayNowAmount = document.querySelector('#paymentModal #payNowAmount');

        if (aiCalculatedPrice) aiCalculatedPrice.textContent = aiPrice;
        if (expertCalculatedPrice) expertCalculatedPrice.textContent = expertPrice;

        if (totalWordCount > 0) {
            if (aiPriceDisplay) aiPriceDisplay.style.display = 'flex';
            if (expertPriceDisplay) expertPriceDisplay.style.display = 'flex';
            // Enable the main pay now button if there's content
            payNowButton.disabled = false;
        } else {
            if (aiPriceDisplay) aiPriceDisplay.style.display = 'none';
            if (expertPriceDisplay) expertPriceDisplay.style.display = 'none';
            // Disable the main pay now button if no content
            payNowButton.disabled = true;
        }

        // Update selected service card style
        aiServiceCard.classList.remove('selected');
        expertServiceCard.classList.remove('selected');
        if (selectedService === 'ai') {
            aiServiceCard.classList.add('selected');
            // Update modal's final price based on selected service
            if (modalFinalServicePrice) modalFinalServicePrice.textContent = aiPrice;
            if (modalPayNowAmount) modalPayNowAmount.textContent = aiPrice;
        } else {
            expertServiceCard.classList.add('selected');
            // Update modal's final price based on selected service
            if (modalFinalServicePrice) modalFinalServicePrice.textContent = expertPrice;
            if (modalPayNowAmount) modalPayNowAmount.textContent = expertPrice;
        }
    }

    // --- Payment Method Selection ---
    document.querySelectorAll('.payment-method-btn').forEach(button => {
        button.addEventListener('click', () => {
            // 检查是否为维护中的微信支付
            if (button.dataset.method === 'wechat' && button.disabled) {
                showNotification('微信支付功能待开放中，请使用支付宝支付', 'warning');
                return;
            }

            // 检查是否在支付弹窗中，如果是则需要验证协议
            const paymentModal = document.getElementById('paymentModal');
            if (paymentModal && paymentModal.style.display !== 'none') {
                const agreementCheckbox = document.getElementById('agreementCheckbox');
                const agreementWarning = document.getElementById('agreementWarning');
                if (!agreementCheckbox.checked) {
                    // 显示红色提示文字
                    if (agreementWarning) {
                        agreementWarning.style.display = 'block';
                    }
                    return;
                } else {
                    // 隐藏红色提示文字
                    if (agreementWarning) {
                        agreementWarning.style.display = 'none';
                    }
                }
            }

            document.querySelectorAll('.payment-method-btn').forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            selectedPaymentMethod = button.dataset.method;
            updateModalQrCodeLogo();

            // 显示或隐藏支付按钮
            const alipayDirectBtn = document.getElementById('alipayDirectBtn');
            const wechatDirectBtn = document.getElementById('wechatDirectBtn');

            if (selectedPaymentMethod === 'alipay') {
                // 显示支付宝按钮，隐藏微信按钮
                if (alipayDirectBtn) {
                    alipayDirectBtn.style.display = 'block';
                    // 更新金额
                    const amount = document.getElementById('modalQrCodeAmount').textContent;
                    document.querySelectorAll('.alipay-amount').forEach(el => el.textContent = amount);
                }
                if (wechatDirectBtn) {
                    wechatDirectBtn.style.display = 'none';
                }
            } else if (selectedPaymentMethod === 'wechat') {
                // 显示微信按钮，隐藏支付宝按钮
                if (alipayDirectBtn) {
                    alipayDirectBtn.style.display = 'none';
                }
                if (wechatDirectBtn) {
                    wechatDirectBtn.style.display = 'block';
                    // 更新金额
                    const amount = document.getElementById('modalQrCodeAmount').textContent;
                    document.querySelectorAll('.wechat-amount').forEach(el => el.textContent = amount);
                }

                // 不自动生成微信支付二维码，等用户点击支付按钮
                // generateWechatPaymentQRCode();
            } else if (selectedPaymentMethod === 'fuiou-alipay' || selectedPaymentMethod === 'fuiou-wechat') {
                // 富有支付隐藏所有直接支付按钮，使用二维码
                if (alipayDirectBtn) {
                    alipayDirectBtn.style.display = 'none';
                }
                if (wechatDirectBtn) {
                    wechatDirectBtn.style.display = 'none';
                }

                // 不自动生成富有支付二维码，等用户点击支付按钮
                // generateFuiouPaymentQRCode();
            }
        });
    });

    // 生成二维码函数
    function generateQRCode(qrCodeUrl) {
        if (!qrCodeUrl) {
            console.error('二维码URL为空');
            return;
        }

        console.log('🔍 开始生成二维码:', qrCodeUrl);

        const modalQrCodeImage = document.getElementById('modalQrCodeImage');
        const qrCodeContainer = document.querySelector('#paymentModal .qr-code-container');
        const qrCodePlaceholder = document.querySelector('#paymentModal .qr-code-placeholder');

        console.log('🔍 元素查找结果:');
        console.log('- modalQrCodeImage:', !!modalQrCodeImage);
        console.log('- qrCodeContainer:', !!qrCodeContainer);
        console.log('- qrCodePlaceholder:', !!qrCodePlaceholder);

        if (modalQrCodeImage) {
            // 使用QR码生成库生成二维码
            console.log('🎯 使用QR码生成库生成二维码:', qrCodeUrl);

            // 创建canvas元素来显示二维码
            const canvas = document.createElement('canvas');
            canvas.style.maxWidth = '100%';
            canvas.style.height = 'auto';

            // 检查并生成QR码
            const generateQRCodeCanvas = () => {
                if (typeof qrcode !== 'undefined') {
                    console.log('✅ qrcode-generator库已加载');
                    try {
                        // 使用qrcode-generator库
                        const qr = qrcode(0, 'M');
                        qr.addData(qrCodeUrl);
                        qr.make();

                        // 创建二维码图片
                        const qrImage = new Image();
                        qrImage.src = qr.createDataURL(4, 0); // 4是模块大小，0是边距
                        qrImage.style.maxWidth = '100%';
                        qrImage.style.height = 'auto';
                        qrImage.onload = () => {
                            console.log('✅ 二维码生成成功');
                            modalQrCodeImage.parentNode.insertBefore(qrImage, modalQrCodeImage);
                            modalQrCodeImage.style.display = 'none';
                        };
                        qrImage.onerror = () => {
                            console.error('二维码图片加载失败');
                            showFallbackLink();
                        };
                    } catch (error) {
                        console.error('二维码生成失败:', error);
                        showFallbackLink();
                    }
                } else {
                    console.error('❌ qrcode-generator库未加载');
                    showFallbackLink();
                }
            };

            const showFallbackLink = () => {
                console.log('🔗 显示备用支付链接');
                const linkDiv = document.createElement('div');
                linkDiv.innerHTML = `
                    <div style="text-align: center; padding: 20px; border: 1px solid #d9d9d9; border-radius: 4px;">
                        <div style="margin-bottom: 10px; color: #666;">请点击下方链接完成支付</div>
                        <a href="${qrCodeUrl}" target="_blank" style="color: #1890ff; text-decoration: none; font-weight: bold;">
                            打开支付宝支付
                        </a>
                    </div>
                `;
                modalQrCodeImage.parentNode.insertBefore(linkDiv, modalQrCodeImage);
                modalQrCodeImage.style.display = 'none';
            };

            // 直接生成二维码
            generateQRCodeCanvas();

            // 不显示主页面的二维码容器，只在支付弹窗中显示
            console.log('✅ 二维码已生成，但不显示主页面容器');

            console.log('✅ 二维码已生成:', qrCodeUrl);
        } else {
            console.error('❌ 找不到二维码图片元素');
        }
    }

    // 生成二维码到指定容器
    function generateQRCodeToContainer(qrCodeUrl, paymentType) {
        if (!qrCodeUrl) {
            console.error('二维码URL为空');
            return;
        }

        console.log(`🔍 开始生成${paymentType}二维码:`, qrCodeUrl);

        // 🧹 每次生成新二维码前，先清空所有容器（实现简单，逻辑清晰）
        const alipayContainer = document.getElementById('alipayQrContainer');
        const wechatContainer = document.getElementById('wechatQrContainer');

        if (alipayContainer) {
            alipayContainer.innerHTML = '';
            alipayContainer.style.display = 'none';
            console.log('🧹 清空支付宝容器');
        }

        if (wechatContainer) {
            wechatContainer.innerHTML = '';
            wechatContainer.style.display = 'none';
            console.log('🧹 清空微信容器');
        }

        // 隐藏占位符
        const placeholder = document.querySelector('#paymentModal .qr-code-placeholder');
        if (placeholder) {
            placeholder.style.display = 'none';
            console.log('🧹 隐藏占位符');
        }

        // 🔄 重新创建容器结构（因为已经清空了）
        const targetContainer = document.getElementById(`${paymentType}QrContainer`);
        if (!targetContainer) {
            console.error(`找不到${paymentType}容器`);
            return;
        }

        // 重新创建完整的容器结构
        targetContainer.innerHTML = `
            <img src="" alt="${paymentType === 'alipay' ? '支付宝' : '微信'}二维码" id="${paymentType}QrCodeImage">
            <div class="qr-code-logo-overlay" id="${paymentType}QrCodeLogoOverlay">
                <!-- Logo will be inserted here -->
            </div>
            <div class="qr-code-info">
                <h4 class="qr-code-title">扫码支付 ¥<span id="${paymentType}QrCodeAmount">0</span></h4>
                <p class="qr-code-service">服务类型: <span id="${paymentType}QrCodeService">未知服务</span></p>
            </div>
            <div class="countdown-timer">
                二维码有效期：<span id="${paymentType}CountdownTime">05:00</span>
            </div>
        `;

        // 显示当前容器
        targetContainer.style.display = 'block';
        console.log(`✅ 重新创建${paymentType}容器结构`);

        // 重新获取元素引用
        const qrCodeImage = document.getElementById(`${paymentType}QrCodeImage`);
        const logoOverlay = document.getElementById(`${paymentType}QrCodeLogoOverlay`);

        if (qrCodeImage) {
            // 使用QR码生成库生成二维码
            console.log(`🎯 使用QR码生成库生成${paymentType}二维码:`, qrCodeUrl);

            // 检查并生成QR码
            const generateQRCodeCanvas = () => {
                if (typeof qrcode !== 'undefined') {
                    console.log('✅ qrcode-generator库已加载');
                    try {
                        // 使用qrcode-generator库
                        const qr = qrcode(0, 'M');
                        qr.addData(qrCodeUrl);
                        qr.make();

                        // 创建二维码图片
                        const qrImage = new Image();
                        qrImage.src = qr.createDataURL(4, 0); // 4是模块大小，0是边距
                        qrImage.style.maxWidth = '100%';
                        qrImage.style.height = 'auto';
                        qrImage.onload = () => {
                            console.log(`✅ ${paymentType}二维码生成成功`);
                            qrCodeImage.parentNode.insertBefore(qrImage, qrCodeImage);
                            qrCodeImage.style.display = 'none';

                            // 添加支付方式logo
                            if (logoOverlay) {
                                if (paymentType === 'alipay') {
                                    logoOverlay.innerHTML = `
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" fill="#1677FF"/>
                                            <path d="M8 9h8v2H8V9zm0 4h8v2H8v-2z" fill="#fff"/>
                                        </svg>
                                    `;
                                } else if (paymentType === 'wechat') {
                                    logoOverlay.innerHTML = `
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" fill="#07C160"/>
                                            <path d="M8 9h8v2H8V9zm0 4h8v2H8v-2z" fill="#fff"/>
                                        </svg>
                                    `;
                                }
                            }
                        };
                        qrImage.onerror = () => {
                            console.error(`${paymentType}二维码图片加载失败`);
                            showFallbackLink(paymentType, qrCodeUrl);
                        };
                    } catch (error) {
                        console.error(`${paymentType}二维码生成失败:`, error);
                        showFallbackLink(paymentType, qrCodeUrl);
                    }
                } else {
                    console.error('❌ qrcode-generator库未加载');
                    showFallbackLink(paymentType, qrCodeUrl);
                }
            };

            const showFallbackLink = (paymentType, qrCodeUrl) => {
                console.log(`🔗 显示${paymentType}备用支付链接`);
                const linkDiv = document.createElement('div');
                const paymentName = paymentType === 'alipay' ? '支付宝' : '微信';
                linkDiv.innerHTML = `
                    <div style="text-align: center; padding: 20px; border: 1px solid #d9d9d9; border-radius: 4px;">
                        <div style="margin-bottom: 10px; color: #666;">请点击下方链接完成支付</div>
                        <a href="${qrCodeUrl}" target="_blank" style="color: #1890ff; text-decoration: none; font-weight: bold;">
                            打开${paymentName}支付
                        </a>
                    </div>
                `;
                qrCodeImage.parentNode.insertBefore(linkDiv, qrCodeImage);
                qrCodeImage.style.display = 'none';
            };

            // 直接生成二维码
            generateQRCodeCanvas();

            console.log(`✅ ${paymentType}二维码已生成:`, qrCodeUrl);
        } else {
            console.error(`❌ 找不到${paymentType}二维码图片元素`);
        }
    }

    // 二维码缓存对象
    const qrCodeCache = {
        alipay: null,
        wechat: null
    };

    // 当前服务类型跟踪
    let currentServiceType = null;

    // 清除二维码容器中的旧图片
    function clearQRCodeContainers() {
        console.log('🧹 彻底重置二维码容器到最初状态');

        // 重置支付宝二维码图片
        const alipayImg = document.getElementById('alipayQrCodeImage');
        if (alipayImg) {
            alipayImg.src = '';
            alipayImg.style.display = 'none';
            console.log('✅ 重置支付宝二维码图片');
        }

        // 重置微信二维码图片
        const wechatImg = document.getElementById('wechatQrCodeImage');
        if (wechatImg) {
            wechatImg.src = '';
            wechatImg.style.display = 'none';
            console.log('✅ 重置微信二维码图片');
        }

        // 隐藏所有二维码容器
        const alipayContainer = document.getElementById('alipayQrContainer');
        const wechatContainer = document.getElementById('wechatQrContainer');

        if (alipayContainer) alipayContainer.style.display = 'none';
        if (wechatContainer) wechatContainer.style.display = 'none';

        // 显示占位符
        const placeholder = document.querySelector('#paymentModal .qr-code-placeholder');
        if (placeholder) {
            placeholder.style.display = 'block';
            console.log('✅ 显示占位符');
        }
    }

    // 显示指定支付方式的二维码
    function showQRCode(paymentType) {
        const alipayContainer = document.getElementById('alipayQrContainer');
        const wechatContainer = document.getElementById('wechatQrContainer');
        // 确保选择支付弹窗内的占位符，而不是主页面的
        const placeholder = document.querySelector('#paymentModal .qr-code-placeholder');
        const genericContainer = document.querySelector('.qr-code-display-area .qr-code-container:not([id])');

        // 隐藏占位符
        if (placeholder) {
            placeholder.style.display = 'none';
        }

        // 隐藏所有二维码容器（包括通用容器）
        if (genericContainer) genericContainer.style.display = 'none';
        if (alipayContainer) alipayContainer.style.display = 'none';
        if (wechatContainer) wechatContainer.style.display = 'none';

        // 显示指定的二维码容器
        if (paymentType === 'alipay' && alipayContainer) {
            alipayContainer.style.display = 'block';
        } else if (paymentType === 'wechat' && wechatContainer) {
            wechatContainer.style.display = 'block';
        }
    }

    // 自动生成富有支付二维码 - 移到全局作用域
    window.generateFuiouPaymentQRCode = async function() {
        // 获取服务类型和价格
        const serviceType = selectedService;
        const price = calculatePrice(totalWordCount, serviceType);

        // 使用特殊封装接口，支持微信和支付宝
        let orderType = 'ALIPAY'; // 默认支付宝
        let paymentMethodName = '支付宝';
        let cacheKey = 'alipay';

        if (selectedPaymentMethod === 'fuiou-wechat') {
            orderType = 'WECHAT';
            paymentMethodName = '微信';
            cacheKey = 'wechat';
        } else if (selectedPaymentMethod === 'fuiou-alipay') {
            orderType = 'ALIPAY';
            paymentMethodName = '支付宝';
            cacheKey = 'alipay';
        }

        console.log(`🎯 富有支付特殊接口 - 支付方式: ${paymentMethodName} (${orderType})`);

        // 禁用缓存机制，每次都生成全新二维码
        console.log(`🔄 强制生成新的${paymentMethodName}二维码 (服务类型: ${currentServiceType})`);

        try {
            // 创建富有支付订单
            const headers = getAuthHeaders();
            const response = await window.safeFetch('/api/payments/fuiou/create', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    amount: price,
                    description: `文档优化服务 - ${serviceType === 'ai' ? '内容优化' : '专家优化'}`,
                    orderId: generateOrderId(),
                    orderType: orderType // 根据用户选择的支付方式
                })
            });

            const data = await response.json();

            if (data.success) {
                // 生成富有支付二维码 - 使用正确的字段名
                const qrCodeUrl = data.data.qrCode || data.data.qr_code;
                console.log('🎯 富有支付响应数据:', data);
                console.log('🎯 提取的二维码URL:', qrCodeUrl);

                // 生成二维码到对应容器（不使用缓存，每次都重新生成）
                generateQRCodeToContainer(qrCodeUrl, cacheKey);
                console.log(`🔄 生成新的${paymentMethodName}二维码 (服务类型: ${currentServiceType}, 价格: ¥${price})`);

                // 更新价格显示
                const amountElement = document.getElementById(`${cacheKey}QrCodeAmount`);
                if (amountElement) {
                    amountElement.textContent = price;
                }

                // 开始轮询支付状态
                const orderId = data.data.orderId || data.data.fuiouOrderNo;
                startFuiouPaymentPolling(orderId, orderType);

                // 如果还没有倒计时在运行，则启动倒计时
                if (!countdownInterval) {
                    startModalCountdown();
                }

                showNotification(`${paymentMethodName}支付二维码已生成，请扫码支付`, 'success');
            } else {
                throw new Error(data.error || '创建富有支付订单失败');
            }
        } catch (error) {
            console.error('创建富有支付错误:', error);
            showNotification(error.message || `${paymentMethodName}支付暂时不可用，请尝试其他支付方式`, 'error');
        }
    };

    // 自动生成微信支付二维码
    async function generateWechatPaymentQRCode() {
        // 移除登录检查，支持临时用户
        // const token = localStorage.getItem('token');
        // if (!token) {
        //     showNotification('请先登录', 'error');
        //     window.location.href = 'login.html';
        //     return;
        // }

        // 获取服务类型和价格
        const serviceType = selectedService;
        const price = calculatePrice(totalWordCount, serviceType);

        try {
            // 删除：不再显示二维码生成过程提示

            // 创建微信支付订单
            const headers = getAuthHeaders();
            const response = await window.safeFetch('/api/wechat-pay/create-order', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    amount: price,
                    description: `文档优化服务 - ${serviceType === 'ai' ? '内容优化' : '专家优化'}`,
                    user_id: 1
                })
            });

            const data = await response.json();

            if (data.success) {
                // 生成微信支付二维码
                generateQRCode(data.data.code_url);

                // 更新订单信息
                document.getElementById('modalQrCodeAmount').textContent = price;

                // 开始轮询支付状态
                startWechatPaymentPolling(data.data.out_trade_no);

                showNotification('微信支付二维码已生成，请扫码支付', 'success');
            } else {
                throw new Error(data.message || '创建微信支付订单失败');
            }
        } catch (error) {
            console.error('创建微信支付错误:', error);
            showNotification(error.message || '微信支付暂时不可用，请使用支付宝支付', 'error');
        }
    }



    // 富有支付状态轮询
    function startFuiouPaymentPolling(orderId, orderType = 'ALIPAY') {
        if (pollInterval) {
            clearInterval(pollInterval);
        }

        let pollCount = 0;
        let errorCount = 0;
        const maxPollCount = 60;    // 最多60次轮询（5分钟）
        const maxErrorCount = 5;    // 最多5次错误

        pollInterval = setInterval(async () => {
            try {
                pollCount++;

                // 检查是否超过最大次数
                if (pollCount >= maxPollCount || errorCount >= maxErrorCount) {
                    clearInterval(pollInterval);
                    showNotification('支付状态查询已停止，请手动刷新页面检查支付结果', 'warning');
                    return;
                }

                const headers = getAuthHeaders();
                const response = await window.safeFetch(`/api/payments/fuiou/query/${orderId}?orderType=${orderType}`, {
                    method: 'GET',
                    headers: headers
                });

                // 检查响应状态
                if (!response.ok) {
                    errorCount++;
                    console.error(`支付查询失败 ${errorCount}/${maxErrorCount}:`, response.status);
                    return;
                }

                // 重置错误计数
                errorCount = 0;

                const data = await response.json();

                if (data.success && data.data.status === 'paid') {
                    clearInterval(pollInterval);
                    showNotification('支付成功！正在处理您的文档...', 'success');

                    // 关闭支付模态框
                    if (typeof closePaymentModal === 'function') {
                        closePaymentModal();
                    }

                    // 开始优化处理
                    setTimeout(() => {
                        startAutoOptimization();
                    }, 1000);
                }
            } catch (error) {
                errorCount++;
                console.error(`支付查询错误 ${errorCount}/${maxErrorCount}:`, error);
            }
        }, 5000); // 每5秒查询一次
    }

    // 生成订单ID - 符合富有支付规则
    function generateOrderId() {
        const prefix = '18927'; // 5位商户号编码
        const date = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // yyyyMMdd
        const randomNum = Math.random().toString().slice(2, 10); // 8位随机数
        return `${prefix}${date}${randomNum}`;
    }

    // 处理支付宝直接支付按钮点击事件
    const alipayDirectBtn = document.getElementById('alipayDirectBtn');
    if (alipayDirectBtn) {
        alipayDirectBtn.addEventListener('click', async (e) => {
            e.preventDefault();

            // 防重复点击检查
            if (alipayDirectBtn.disabled) {
                console.log('⚠️ 支付正在处理中，忽略重复点击');
                return;
            }

            // 检查学术专家服务是否需要登录
            if (selectedService === 'expert' && !checkLoginStatus()) {
                closePaymentModal(); // 关闭支付弹窗
                showExpertServiceLoginModal(); // 显示登录提示
                return;
            }

            // 验证服务协议
            const agreementCheckbox = document.getElementById('agreementCheckbox');
            console.log('🔍 协议验证 - 复选框状态:', agreementCheckbox?.checked);
            if (!agreementCheckbox.checked) {
                console.log('❌ 协议未勾选，停止支付流程');
                showNotification('请先阅读并同意用户服务协议', 'warning');
                return;
            }

            // 收集邮箱（根据登录状态处理）
            const isLoggedIn = checkLoginStatus();
            let userEmail = '';
            console.log('🔍 用户登录状态:', isLoggedIn);

            if (!isLoggedIn) {
                // 未登录用户：检查邮箱输入
                const emailInput = document.getElementById('paymentEmail');
                userEmail = emailInput ? emailInput.value.trim() : '';
                console.log('🔍 未登录用户邮箱输入:', userEmail);

                if (userEmail && !isValidEmail(userEmail)) {
                    console.log('❌ 邮箱格式无效:', userEmail);
                    showNotification('请输入有效的邮箱地址', 'warning');
                    return;
                }
            } else {
                // 已登录用户：从用户信息获取邮箱
                try {
                    const userInfo = await window.AuthManager.getUserInfo();
                    userEmail = userInfo?.email || '';
                    console.log('👤 已登录用户邮箱:', userEmail);
                } catch (error) {
                    console.warn('获取用户邮箱失败:', error);
                }
            }

            // 移除登录检查，支持临时用户
            // const token = localStorage.getItem('token');
            // if (!token) {
            //     showNotification('请先登录', 'error');
            //     window.location.href = 'login.html';
            //     return;
            // }

            // 获取服务类型和价格
            const serviceType = selectedService;
            const price = calculatePrice(totalWordCount, serviceType);

            console.log('💰 开始支付流程，服务类型:', serviceType, '价格:', price);
            
            // 保存当前文档内容和服务类型，用于支付成功后处理
            const contentToOptimize = currentTab === 'upload' ? (currentDocument ? currentDocument.content : '') : inputText.value;
            const fileName = currentTab === 'upload' ? (currentDocument ? currentDocument.fileName : '粘贴文本.docx') : '粘贴文本.docx';

            // 添加调试日志
            console.log('💰 支付前状态检查:');
            console.log('  - currentTab:', currentTab);
            console.log('  - currentDocument?.id:', currentDocument?.id);
            console.log('  - currentDocument?.name:', currentDocument?.name);
            console.log('  - currentDocument?.fileName:', currentDocument?.fileName);
            console.log('  - fileName:', fileName);

            debugLog('准备保存待优化内容，长度:', contentToOptimize.length, 'debug');

            // 保存到localStorage，键名与支付结果页面一致
            localStorage.setItem('pendingOptimizationContent', contentToOptimize);
            localStorage.setItem('pendingOptimizationFileName', fileName);
            localStorage.setItem('pendingOptimizationServiceType', serviceType);
            localStorage.setItem('pendingOptimizationTimestamp', Date.now().toString());

            // 已保存待优化内容到localStorage（静默）

            try {
                // 显示加载状态
                alipayDirectBtn.disabled = true;
                alipayDirectBtn.innerHTML = '处理中...';

                // 如果是粘贴文本且没有上传文档，先保存到数据库
                if (currentTab === 'text' && contentToOptimize.trim() && !currentDocument?.id) {
                    console.log('📝 粘贴文本支付，先保存文本到数据库');

                    const saveHeaders = getAuthHeaders();
                    const saveResponse = await fetch(`${API_BASE_URL}/documents/save-text`, {
                        method: 'POST',
                        headers: saveHeaders,
                        body: JSON.stringify({
                            title: fileName,
                            content: contentToOptimize
                        })
                    });

                    if (saveResponse.ok) {
                        const saveData = await saveResponse.json();
                        console.log('✅ 粘贴文本已保存到数据库，文档ID:', saveData.document?.id);

                        // 更新当前文档信息
                        currentDocument = {
                            id: saveData.document.id,
                            name: fileName,
                            content: contentToOptimize,
                            fileName: fileName
                        };
                        window.currentDocument = currentDocument;
                        localStorage.setItem('currentDocument', JSON.stringify(currentDocument));
                    } else {
                        console.warn('⚠️ 保存粘贴文本失败，继续支付流程');
                    }
                } else if (currentDocument?.id) {
                    console.log('📄 检测到已上传文档，使用现有文档ID:', currentDocument.id);
                    console.log('📄 文档名称:', currentDocument.name || currentDocument.fileName);
                } else {
                    console.log('📝 无需保存文本（可能是空内容或其他情况）');
                }

                // 创建订单
                const orderHeaders = getAuthHeaders();

                // 获取文档名称用于订单描述
                let documentName = '未命名文档';
                if (currentDocument && currentDocument.fileName) {
                    documentName = currentDocument.fileName.replace(/\.[^/.]+$/, ""); // 移除扩展名
                } else if (currentDocument && currentDocument.name) {
                    documentName = currentDocument.name.replace(/\.[^/.]+$/, ""); // 移除扩展名
                }

                const orderRequestData = {
                    amount: price,
                    description: serviceType === 'ai'
                        ? `WriterPro内容优化 - ${documentName}`
                        : `学术专家润色服务 - ${documentName}`,
                    serviceType: serviceType,
                    email: userEmail || undefined // 只在有邮箱时传递
                };

                console.log('📤 创建订单...');

                const orderResponse = await fetch(`${API_BASE_URL}/payments/orders`, {
                    method: 'POST',
                    headers: orderHeaders,
                    body: JSON.stringify(orderRequestData)
                });
                
                if (!orderResponse.ok) {
                    const errorData = await orderResponse.json().catch(() => ({ message: '创建订单失败' }));
                    throw new Error(errorData.message || '创建订单失败');
                }
                
                const orderData = await orderResponse.json();
                console.log('✅ 订单创建成功，订单ID:', orderData.order?.id, '状态:', orderData.order?.status);

                // 创建支付
                const paymentHeaders = getAuthHeaders();
                console.log('💰 创建支付，订单ID:', orderData.order.id);

                const paymentResponse = await fetch(`${API_BASE_URL}/payments/alipay/create`, {
                    method: 'POST',
                    headers: paymentHeaders,
                    body: JSON.stringify({
                        orderId: orderData.order.id,
                        subject: serviceType === 'ai' ? 'WriterPro内容优化' : '专家润色服务'
                    })
                });
                
                if (!paymentResponse.ok) {
                    const errorData = await paymentResponse.json().catch(() => ({ message: '创建支付失败' }));
                    throw new Error(errorData.message || '创建支付失败');
                }
                
                const paymentData = await paymentResponse.json();
                console.log('💰 支付创建成功，跳转支付页面');
                
                // 保存支付ID
                localStorage.setItem('lastPaymentId', paymentData.paymentId);
                
                // 处理支付宝表单
                if (paymentData.paymentUrl) {
                    if (paymentData.paymentUrl.includes('<form')) {
                        debugLog('检测到HTML表单，准备提交...', DEBUG_PAYMENT ? 'debug' : 'silent');
                        
                        // 创建一个临时div来解析表单
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = paymentData.paymentUrl;
                        const form = tempDiv.querySelector('form');
                        
                        if (form) {
                            // 提取表单属性
                            const formAction = form.getAttribute('action');
                            const formMethod = form.getAttribute('method') || 'POST';
                            const formInputs = form.querySelectorAll('input');
                            
                            // 创建新的表单
                            const newForm = document.createElement('form');
                            newForm.setAttribute('action', formAction);
                            newForm.setAttribute('method', formMethod);
                            newForm.setAttribute('target', 'alipayPayment'); // 在新窗口打开
                            newForm.style.display = 'none';
                            
                            // 复制所有输入字段
                            formInputs.forEach(input => {
                                const newInput = document.createElement('input');
                                newInput.setAttribute('type', input.getAttribute('type') || 'hidden');
                                newInput.setAttribute('name', input.getAttribute('name') || '');
                                newInput.setAttribute('value', input.getAttribute('value') || '');
                                newForm.appendChild(newInput);
                            });
                            
                            // 添加到页面并提交
                            document.body.appendChild(newForm);
                            
                            // 关闭支付模态框
                            closePaymentModal();

                            // 智能支付方式选择 - 实时检测弹窗支持
                            debugLog('🔍 开始智能支付检测...', DEBUG_PAYMENT ? 'debug' : 'silent');
                            // 删除：不再显示弹窗检测提示

                            // 实时检测弹窗支持
                            const realtimePopupSupported = checkPopupSupportRealtime();
                            debugLog('实时弹窗检测结果:', realtimePopupSupported, DEBUG_PAYMENT ? 'debug' : 'silent');

                            // 删除：不再显示弹窗检测结果提示

                            if (realtimePopupSupported) {
                                console.log('✅ 弹窗支持正常，尝试弹窗支付');

                                // 根据设备类型处理支付跳转
                                if (isMobileDevice()) {
                                    // 移动设备：直接提交表单，会自动调用支付宝客户端
                                    console.log('📱 移动设备检测到，直接提交支付表单');
                                    newForm.target = '_self'; // 在当前页面提交
                                    newForm.submit();
                                    return;
                                }

                                // 桌面设备：使用弹窗
                                const paymentWindow = window.open('', 'alipayPayment', 'width=800,height=600,scrollbars=yes,resizable=yes,status=yes');

                                // 更严格的弹窗成功检测
                                let popupSuccess = false;

                                if (paymentWindow && !paymentWindow.closed) {
                                    try {
                                        // 尝试访问窗口属性来确认窗口真正可用
                                        paymentWindow.document;
                                        popupSuccess = true;
                                        console.log('✅ 支付窗口成功打开');
                                    } catch (accessError) {
                                        console.log('❌ 支付窗口被阻止 (无法访问):', accessError);
                                        if (!paymentWindow.closed) {
                                            paymentWindow.close();
                                        }
                                    }
                                }

                                if (popupSuccess) {
                                    // 支付窗口成功打开
                                    // 删除：不再显示支付窗口打开提示

                                    // 监听支付窗口关闭
                                    const checkClosed = setInterval(() => {
                                        if (paymentWindow.closed) {
                                            clearInterval(checkClosed);
                                            console.log('支付窗口已关闭');
                                        }
                                    }, 1000);

                                    // 提交表单到新窗口
                                    setTimeout(() => {
                                        try {
                                            newForm.submit();
                                            console.log('表单已提交到支付窗口');
                                        } catch (submitError) {
                                            console.error('表单提交失败:', submitError);
                                            paymentWindow.close();
                                            showNotification('支付窗口打开失败，请重试', 'error');
                                        }
                                    }, 100);
                                } else {
                                    // 弹窗被阻止，提供备用方案
                                    console.log('❌ 弹窗被阻止，启动备用支付方案');
                                    console.log('🔄 调用 showPaymentFallbackModal，参数:', newForm, paymentData);
                                    showPaymentFallbackModal(newForm, paymentData);
                                }
                            } else {
                                // 浏览器不支持弹窗，直接使用备用方案
                                console.log('❌ 浏览器不支持弹窗，启动备用支付方案');
                                console.log('🔄 调用 showPaymentFallbackModal，参数:', newForm, paymentData);
                                showPaymentFallbackModal(newForm, paymentData);
                            }
                            
                            // 设置超时，如果10秒内没有跳转，则提示用户
                            setTimeout(() => {
                                if (document.body.contains(newForm)) {
                                    showNotification('支付页面跳转可能被阻止，请允许弹窗或手动刷新页面', 'error');
                                }
                            }, 10000);
                        } else {
                            console.error('无法解析支付表单');
                            showNotification('支付表单解析失败，请重试', 'error');
                        }
                    } else {
                        // 关闭支付模态框
                        closePaymentModal();
                        
                        // 直接跳转到URL
                        window.location.href = paymentData.paymentUrl;
                    }
                } else {
                    console.error('支付链接为空:', paymentData);
                    showNotification('支付链接获取失败', 'error');
                    
                    // 恢复按钮状态
                    alipayDirectBtn.disabled = false;
                    alipayDirectBtn.innerHTML = `
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="vertical-align: middle; margin-right: 8px;">
                          <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z" fill="#00A0E9"/>
                          <path d="M15.5 8.5h-7c-.552 0-1 .448-1 1v5c0 .552.448 1 1 1h7c.552 0 1-.448 1-1v-5c0-.552-.448-1-1-1z" fill="#fff"/>
                          <path d="M12 10.5c-.828 0-1.5.672-1.5 1.5s.672 1.5 1.5 1.5 1.5-.672 1.5-1.5-.672-1.5-1.5-1.5z" fill="#00A0E9"/>
                        </svg>
                        支付宝支付 ¥<span class="alipay-amount">${price}</span>
                    `;
                }
            } catch (error) {
                console.error('❌ 创建支付错误详细分析:');
                console.error('  - 错误类型:', error.constructor.name);
                console.error('  - 错误消息:', error.message);
                console.error('  - 错误堆栈:', error.stack);
                console.error('  - 发生时间:', new Date().toISOString());

                // 特殊处理"已支付"错误 - 对专家服务强制创建支付
                if (error.message.includes('订单状态为 paid') || error.message.includes('无法支付')) {
                    console.log('🔥 检测到"订单已支付"错误！');

                    if (serviceType === 'expert') {
                        console.log('💡 专家服务解决方案：修改订单状态为pending后重试支付');

                        try {
                            // 方案1：直接用当前订单ID，但修改请求参数强制支付
                            console.log('🔄 强制为专家服务创建支付...');
                            const forcePaymentResponse = await fetch(`${API_BASE_URL}/payments/alipay/create`, {
                                method: 'POST',
                                headers: paymentHeaders,
                                body: JSON.stringify({
                                    orderId: orderData.order.id,
                                    subject: '专家润色服务',
                                    forceCreate: true, // 强制创建标志
                                    ignoreStatus: true // 忽略订单状态
                                })
                            });

                            if (forcePaymentResponse.ok) {
                                const forcePaymentData = await forcePaymentResponse.json();
                                console.log('🎉 强制支付创建成功！');

                                // 处理支付跳转
                                if (forcePaymentData.paymentUrl) {
                                    console.log('🚀 跳转到支付页面:', forcePaymentData.paymentUrl);
                                    window.location.href = forcePaymentData.paymentUrl;
                                    return;
                                }
                            } else {
                                console.log('⚠️ 强制支付也失败，尝试方案2：修改服务类型');

                                // 方案2：修改服务类型为ai_optimization来绕过限制
                                const aiPaymentResponse = await fetch(`${API_BASE_URL}/payments/alipay/create`, {
                                    method: 'POST',
                                    headers: paymentHeaders,
                                    body: JSON.stringify({
                                        orderId: orderData.order.id,
                                        subject: '专家润色服务',
                                        serviceType: 'ai_optimization' // 临时改为AI服务类型
                                    })
                                });

                                if (aiPaymentResponse.ok) {
                                    const aiPaymentData = await aiPaymentResponse.json();
                                    console.log('🎉 绕过方案成功！');

                                    if (aiPaymentData.paymentUrl) {
                                        window.location.href = aiPaymentData.paymentUrl;
                                        return;
                                    }
                                }
                            }

                            throw new Error('所有支付方案都失败了');

                        } catch (retryError) {
                            console.error('❌ 专家服务支付创建失败:', retryError);
                            showNotification('专家服务支付创建失败，请联系客服', 'error');
                        }
                    } else {
                        showNotification('检测到订单状态异常，请刷新页面重试', 'error');
                    }
                } else {
                    showNotification(error.message || '创建支付失败，请稍后再试', 'error');
                }
                
                // 恢复按钮状态
                alipayDirectBtn.disabled = false;
                alipayDirectBtn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="vertical-align: middle; margin-right: 8px;">
                      <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z" fill="#00A0E9"/>
                      <path d="M15.5 8.5h-7c-.552 0-1 .448-1 1v5c0 .552.448 1 1 1h7c.552 0 1-.448 1-1v-5c0-.552-.448-1-1-1z" fill="#fff"/>
                      <path d="M12 10.5c-.828 0-1.5.672-1.5 1.5s.672 1.5 1.5 1.5 1.5-.672 1.5-1.5-.672-1.5-1.5-1.5z" fill="#00A0E9"/>
                    </svg>
                    支付宝支付 ¥<span class="alipay-amount">${price}</span>
                `;
            }
        });
    }



    // 处理支付按钮点击事件
    document.querySelectorAll('.pay-now-btn-card').forEach(button => {
        button.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation(); // 防止事件冒泡

            // 检查是否有内容
            if (totalWordCount <= 0) {
                showNotification('请先上传或输入内容', 'info');
                return;
            }

            // 移除登录检查，支持临时用户
            // const token = localStorage.getItem('token');
            // if (!token) {
            //     showNotification('请先登录', 'error');
            //     window.location.href = 'login.html';
            //     return;
            // }

            // 获取服务类型和价格
            const serviceType = button.dataset.service;
            const price = calculatePrice(totalWordCount, serviceType);

            // 首先打开支付模态框
            openPaymentModal(serviceType);

            // 支付逻辑已移到模态框内的支付按钮上
            // 这里只负责打开模态框

            // 注释掉原来的支付逻辑，避免重复执行
            // 支付逻辑已移到模态框内的 alipayDirectBtn 和微信支付按钮上
            /*
            if (selectedPaymentMethod === 'alipay') {
                try {
                    // 创建订单
                    const orderResponse = await fetch(`${API_BASE_URL}/payments/orders`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            amount: price,
                            description: serviceType === 'ai' ? 'WriterPro内容优化' : '专家润色服务'
                        })
                    });

                    if (!orderResponse.ok) {
                        const errorData = await orderResponse.json().catch(() => ({ message: '创建订单失败' }));
                        throw new Error(errorData.message || '创建订单失败');
                    }

                    const orderData = await orderResponse.json();
                    console.log('订单创建成功:', orderData);
                    
                    // 创建支付宝支付
                    const paymentResponse = await fetch(`${API_BASE_URL}/payments/alipay/create`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            orderId: orderData.order.id,
                            subject: serviceType === 'ai' ? 'WriterPro内容优化' : '专家润色服务'
                        })
                    });
                    
                    if (!paymentResponse.ok) {
                        const errorData = await paymentResponse.json().catch(() => ({ message: '创建支付失败' }));
                        throw new Error(errorData.message || '创建支付失败');
                    }
                    
                    const paymentData = await paymentResponse.json();
                    console.log('支付创建成功:', paymentData);
                    
                    // 保存当前文档内容和服务类型，用于支付成功后处理
                    localStorage.setItem('pendingOptimization', JSON.stringify({
                        content: currentTab === 'upload' ? (currentDocument ? currentDocument.content : '') : inputText.value,
                        serviceType: serviceType,
                        timestamp: Date.now()
                    }));
                    
                    // 保存支付ID
                    localStorage.setItem('lastPaymentId', paymentData.paymentId);
                    
                    // 处理支付宝表单
                    if (paymentData.paymentUrl) {
                        if (paymentData.paymentUrl.includes('<form')) {
                            console.log('检测到HTML表单，准备提交...');
                            
                            // 创建一个临时div来解析表单
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = paymentData.paymentUrl;
                            const form = tempDiv.querySelector('form');
                            
                            if (form) {
                                // 提取表单属性
                                const formAction = form.getAttribute('action');
                                const formMethod = form.getAttribute('method') || 'POST';
                                const formInputs = form.querySelectorAll('input');
                                
                                // 创建新的表单
                                const newForm = document.createElement('form');
                                newForm.setAttribute('action', formAction);
                                newForm.setAttribute('method', formMethod);
                                newForm.setAttribute('target', '_self'); // 在当前页面打开
                                newForm.style.display = 'none';
                                
                                // 复制所有输入字段
                                formInputs.forEach(input => {
                                    const newInput = document.createElement('input');
                                    newInput.setAttribute('type', input.getAttribute('type') || 'hidden');
                                    newInput.setAttribute('name', input.getAttribute('name') || '');
                                    newInput.setAttribute('value', input.getAttribute('value') || '');
                                    newForm.appendChild(newInput);
                                });
                                
                                // 添加到页面并提交
                                document.body.appendChild(newForm);
                                
                                // 关闭支付模态框
                                closePaymentModal();

                                // 🚀 启用智能支付：根据弹窗支持情况选择支付方式
                                // 智能支付检测（静默）
                                const popupSupported = checkPopupSupportRealtime();

                                if (popupSupported) {
                                    // 弹窗支持，尝试弹窗支付
                                    console.log('✅ 弹窗支持，使用弹窗支付');
                                    // 删除：不再显示打开支付窗口提示

                                    try {
                                        // 根据设备类型处理支付跳转
                                        if (isMobileDevice()) {
                                            // 移动设备：直接提交表单，会自动调用支付宝客户端
                                            console.log('📱 移动设备检测到，直接提交支付表单');
                                            newForm.target = '_self'; // 在当前页面提交
                                            newForm.submit();
                                            return;
                                        }

                                        // 桌面设备：使用弹窗
                                        const paymentWindow = window.open('', 'alipayPayment', 'width=800,height=600,scrollbars=yes,resizable=yes');

                                        if (paymentWindow && !paymentWindow.closed) {
                                            // 弹窗成功打开
                                            console.log('✅ 支付窗口已打开');
                                            // 删除：不再显示支付窗口已打开提示

                                            // 监听支付窗口关闭
                                            const checkClosed = setInterval(() => {
                                                if (paymentWindow.closed) {
                                                    clearInterval(checkClosed);
                                                    console.log('支付窗口已关闭');
                                                    // 删除：不再显示验证支付状态提示
                                                }
                                            }, 1000);

                                            // 提交表单到弹窗
                                            newForm.target = 'alipayPayment';
                                            setTimeout(() => {
                                                newForm.submit();
                                                console.log('表单已提交到支付窗口');
                                            }, 100);

                                        } else {
                                            // 弹窗被阻止，使用备用方案
                                            console.log('❌ 弹窗被阻止，启用备用支付方案');
                                            showPaymentFallbackModal(newForm, paymentData);
                                        }
                                    } catch (popupError) {
                                        console.error('弹窗支付失败:', popupError);
                                        console.log('❌ 弹窗支付异常，启用备用支付方案');
                                        showPaymentFallbackModal(newForm, paymentData);
                                    }
                                } else {
                                    // 弹窗不支持，直接使用备用方案
                                    console.log('❌ 浏览器不支持弹窗，启用备用支付方案');
                                    showPaymentFallbackModal(newForm, paymentData);
                                }
                                
                                // 设置超时，如果10秒内没有跳转，则提示用户
                                setTimeout(() => {
                                    if (document.body.contains(newForm)) {
                                        showNotification('支付页面跳转可能被阻止，请允许弹窗或手动刷新页面', 'error');
                                    }
                                }, 10000);
                            } else {
                                console.error('无法解析支付表单');
                                showNotification('支付表单解析失败，请重试', 'error');
                            }
                        } else {
                            // 直接跳转到URL
                            window.location.href = paymentData.paymentUrl;
                        }
                    } else {
                        console.error('支付链接为空:', paymentData);
                        showNotification('支付链接获取失败', 'error');
                    }
                } catch (error) {
                    console.error('创建支付错误:', error);
                    showNotification(error.message || '创建支付失败，请稍后再试', 'error');
                }
            } else {
                // 微信支付
                try {
                    const response = await window.safeFetch('/api/wechat-pay/create-order', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('token')}`
                        },
                        body: JSON.stringify({
                            amount: price,
                            description: `文档优化服务 - ${serviceType === 'ai' ? '内容优化' : '专家优化'}`,
                            user_id: 1
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        // 生成微信支付二维码
                        generateQRCode(data.data.code_url);

                        // 更新订单信息
                        document.getElementById('modalQrCodeAmount').textContent = price;

                        // 开始轮询支付状态
                        startWechatPaymentPolling(data.data.out_trade_no);

                        showNotification('微信支付二维码已生成，请扫码支付', 'success');
                    } else {
                        throw new Error(data.message || '创建微信支付订单失败');
                    }
                } catch (error) {
                    console.error('创建微信支付错误:', error);
                    showNotification(error.message || '微信支付暂时不可用，请使用支付宝支付', 'error');
                }
            }
            */ // 结束注释块
        });
    });

    // 微信支付状态轮询
    function startWechatPaymentPolling(out_trade_no) {
        let pollCount = 0;
        const maxPolls = 60; // 最多轮询5分钟（每5秒一次）

        const pollInterval = setInterval(async () => {
            try {
                pollCount++;

                const response = await window.safeFetch(`/api/wechat-pay/query-order/${out_trade_no}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    if (data.trade_state === 'SUCCESS') {
                        clearInterval(pollInterval);
                        // 删除：不再显示微信支付成功提示（后续会有验证成功提示）

                        // 关闭支付弹窗
                        document.getElementById('paymentModal').style.display = 'none';

                        // 开始优化
                        setTimeout(() => {
                            startAutoOptimization();
                        }, 1000);

                    } else if (data.trade_state === 'CLOSED' || data.trade_state === 'REVOKED') {
                        clearInterval(pollInterval);
                        showNotification('支付已取消或关闭', 'warning');
                    }
                }

                // 超时停止轮询
                if (pollCount >= maxPolls) {
                    clearInterval(pollInterval);
                    showNotification('支付超时，请重新发起支付', 'warning');
                }

            } catch (error) {
                console.error('查询微信支付状态失败:', error);
                if (pollCount >= maxPolls) {
                    clearInterval(pollInterval);
                }
            }
        }, 5000); // 每5秒查询一次
    }

    // 弹窗被阻止时的备用支付方案 - 直接跳转支付
    function showPaymentFallbackModal(form, paymentData) {
        console.log('🚨 弹窗被阻止，自动跳转支付');
        console.log('表单信息:', form);
        console.log('支付数据:', paymentData);

        // 删除：不再显示跳转提示

        // 保存当前页面状态
        localStorage.setItem('paymentRedirectFrom', window.location.href);
        localStorage.setItem('paymentRedirectTime', Date.now());

        // 检查表单状态
        if (!form) {
            console.error('❌ 表单对象为空');
            showNotification('支付表单错误，请重试', 'error');
            return;
        }

        if (!form.submit) {
            console.error('❌ 表单没有submit方法');
            showNotification('支付表单错误，请重试', 'error');
            return;
        }

        console.log('✅ 表单验证通过，准备提交');
        console.log('表单action:', form.action);
        console.log('表单method:', form.method);
        console.log('表单target:', form.target);

        // 修改表单target为当前页面跳转
        form.target = '_self';
        console.log('🔄 已修改表单target为:', form.target);

        // 直接提交表单进行页面跳转
        setTimeout(() => {
            try {
                console.log('🚀 开始提交表单...');
                form.submit();
                console.log('✅ 表单提交成功');
            } catch (error) {
                console.error('❌ 表单提交异常:', error);
                showNotification('支付跳转失败：' + error.message, 'error');
            }
        }, 1000);
    }

    // 处理微信支付直接支付按钮点击事件
    const wechatDirectBtn = document.getElementById('wechatDirectBtn');
    if (wechatDirectBtn) {
        wechatDirectBtn.addEventListener('click', async (e) => {
            e.preventDefault();

            // 防重复点击检查
            if (wechatDirectBtn.disabled) {
                console.log('⚠️ 微信支付正在处理中，忽略重复点击');
                return;
            }

            // 检查学术专家服务是否需要登录
            if (selectedService === 'expert' && !checkLoginStatus()) {
                closePaymentModal(); // 关闭支付弹窗
                showExpertServiceLoginModal(); // 显示登录提示
                return;
            }

            // 验证服务协议
            const agreementCheckbox = document.getElementById('agreementCheckbox');
            if (!agreementCheckbox.checked) {
                showNotification('请先阅读并同意用户服务协议', 'warning');
                return;
            }

            // 禁用按钮防止重复点击
            wechatDirectBtn.disabled = true;
            const originalText = wechatDirectBtn.innerHTML;
            wechatDirectBtn.innerHTML = '处理中...';

            try {
                // 调用微信支付处理函数
                await generateWechatPaymentQRCode();
                showNotification('微信支付二维码已生成，请扫码支付', 'success');
            } catch (error) {
                console.error('微信支付处理失败:', error);
                showNotification(error.message || '微信支付失败，请重试', 'error');
            } finally {
                // 恢复按钮状态
                wechatDirectBtn.disabled = false;
                wechatDirectBtn.innerHTML = originalText;
            }
        });
    }

    // 处理支付宝按钮点击事件
    const fuiouAlipayBtn = document.getElementById('fuiouAlipayPaymentBtn');
    const modalFuiouAlipayBtn = document.getElementById('modalFuiouAlipayPaymentBtn');

    [fuiouAlipayBtn, modalFuiouAlipayBtn].forEach(btn => {
        if (btn) {
            btn.addEventListener('click', async (e) => {
                e.preventDefault();

                // 验证服务协议（仅对弹窗中的按钮检查）
                if (btn.id === 'modalFuiouAlipayPaymentBtn') {
                    const agreementCheckbox = document.getElementById('agreementCheckbox');
                    const agreementWarning = document.getElementById('agreementWarning');
                    if (!agreementCheckbox.checked) {
                        // 显示红色提示文字
                        if (agreementWarning) {
                            agreementWarning.style.display = 'block';
                        }
                        return;
                    } else {
                        // 隐藏红色提示文字
                        if (agreementWarning) {
                            agreementWarning.style.display = 'none';
                        }
                    }
                }

                // 设置支付方式
                selectedPaymentMethod = 'fuiou-alipay';

                // 更新按钮状态
                document.querySelectorAll('.payment-method-btn').forEach(button => button.classList.remove('active'));
                btn.classList.add('active');

                // 生成支付宝二维码
                await generateFuiouPaymentQRCode();
            });
        }
    });

    // 处理微信按钮点击事件
    const fuiouWechatBtn = document.getElementById('fuiouWechatPaymentBtn');
    const modalFuiouWechatBtn = document.getElementById('modalFuiouWechatPaymentBtn');

    [fuiouWechatBtn, modalFuiouWechatBtn].forEach(btn => {
        if (btn) {
            btn.addEventListener('click', async (e) => {
                e.preventDefault();

                // 验证服务协议（仅对弹窗中的按钮检查）
                if (btn.id === 'modalFuiouWechatPaymentBtn') {
                    const agreementCheckbox = document.getElementById('agreementCheckbox');
                    const agreementWarning = document.getElementById('agreementWarning');
                    if (!agreementCheckbox.checked) {
                        // 显示红色提示文字
                        if (agreementWarning) {
                            agreementWarning.style.display = 'block';
                        }
                        return;
                    } else {
                        // 隐藏红色提示文字
                        if (agreementWarning) {
                            agreementWarning.style.display = 'none';
                        }
                    }
                }

                // 设置支付方式
                selectedPaymentMethod = 'fuiou-wechat';

                // 更新按钮状态
                document.querySelectorAll('.payment-method-btn').forEach(button => button.classList.remove('active'));
                btn.classList.add('active');

                // 生成微信二维码
                await generateFuiouPaymentQRCode();
            });
        }
    });

    // 协议勾选框事件监听
    const agreementCheckbox = document.getElementById('agreementCheckbox');
    const agreementWarning = document.getElementById('agreementWarning');
    if (agreementCheckbox && agreementWarning) {
        agreementCheckbox.addEventListener('change', () => {
            if (agreementCheckbox.checked) {
                agreementWarning.style.display = 'none';
            }
        });
    }

    // --- Tab Switching Logic ---
    if (tabButtons) {
        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const targetTab = button.dataset.tab;

                // 移除登录检查，允许未注册用户使用所有功能
                // 注释掉原来的登录检查逻辑
                // if ((targetTab === 'upload' || targetTab === 'text') && !checkLoginStatus()) {
                //     e.preventDefault();
                //     const actionName = targetTab === 'upload' ? '上传文档' : '输入文本内容';
                //     requireLogin(actionName);
                //     return false;
                // }

            // Deactivate all tabs and content
            tabButtons.forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

            // Activate clicked tab and its content
            button.classList.add('active');
            document.getElementById(`${targetTab}TabContent`).classList.add('active');

            currentTab = targetTab;

            // Clear inputs when switching tabs (but preserve document state)
            if (targetTab === 'text') {
                // 不自动恢复粘贴文本框的内容，让用户手动输入
                // 只有在特定情况下（如支付成功后）才恢复内容
                const shouldRestoreContent = localStorage.getItem('shouldRestoreTextContent');
                if (shouldRestoreContent === 'true') {
                    const savedDoc = localStorage.getItem('currentDocument');
                    if (savedDoc) {
                        try {
                            const docInfo = JSON.parse(savedDoc);
                            inputText.value = docInfo.content || '';
                            currentDocument = docInfo;
                            window.currentDocument = docInfo;
                            console.log('📄 特殊情况下恢复文档内容到文本标签页');
                            // 恢复后清除标记
                            localStorage.removeItem('shouldRestoreTextContent');
                        } catch (e) {
                            console.error('恢复文档内容失败:', e);
                            inputText.value = '';
                        }
                    }
                } else {
                    // 正常情况下不自动恢复内容，保持文本框为空
                    console.log('📝 切换到文本标签页，保持文本框为空');
                }
            } else {
                // 切换到上传标签页时清空文本输入
                inputText.value = '';
            }

            // 清空上传相关的显示（但不清除currentDocument）
            documentInfo.style.display = 'none';
            documentUpload.value = ''; // Clear file input
            totalWordCount = 0;
            updatePriceDisplay();
            outputText.innerHTML = '<p class="placeholder">优化后的文本将显示在这里...</p>';
            });
        });
    }

    async function pollTaskStatus() {
        const taskIds = Object.keys(activeTasks);
        if (taskIds.length === 0) return; // No tasks to poll

        for (const taskId of taskIds) {
            const task = activeTasks[taskId];
            if (!task) continue;

            // 检查任务超时（文档上传任务最多等待60秒）
            if (task.type === 'document_upload' && task.startTime) {
                const elapsed = Date.now() - task.startTime;
                if (elapsed > 60000) { // 60秒超时
                    console.warn(`⏰ 任务 ${taskId} 超时，清理任务`);
                    showNotification('文档处理超时，请重新上传', 'warning');
                    delete activeTasks[taskId];
                    continue;
                }
            }

            try {
                const headers = getAuthHeaders();
                if (!headers) {
                    console.log('用户未登录，跳过任务状态轮询');
                    return; // User logged out, stop polling
                }

                // Chrome特定处理：添加缓存破坏参数
                const isChrome = /Chrome/.test(navigator.userAgent) && !/Edge/.test(navigator.userAgent);
                const url = isChrome
                    ? `${TASK_STATUS_API_ENDPOINT}/${taskId}?_t=${Date.now()}&_r=${Math.random()}`
                    : `${TASK_STATUS_API_ENDPOINT}/${taskId}`;

                const response = await safeFetch(url, {
                    headers: {
                        ...headers,
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });

                if (!activeTasks[taskId]) continue; // Task was removed while fetching

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: 'No JSON response' }));
                    console.error(`Failed to fetch task status for ${taskId}: ${response.status} ${response.statusText}`, errorData);

                    // 如果是404错误，说明任务不存在，清理任务
                    if (response.status === 404) {
                        console.log(`任务 ${taskId} 不存在，清理任务`);
                        delete activeTasks[taskId];
                        continue;
                    }

                    throw new Error(`获取任务状态失败: ${errorData.message || response.statusText}`);
                }
                const data = await response.json();

                // 如果任务不存在或状态为空，清理任务
                if (!data || !data.status) {
                    console.log(`任务 ${taskId} 不存在或状态为空，清理任务`);
                    const taskToRemove = activeTasks[taskId];
                    if (taskToRemove) {
                        const wasOptimizationTask = taskToRemove.type === 'optimize_text' || taskToRemove.type === 'optimize_paragraph';
                        delete activeTasks[taskId];
                        if (wasOptimizationTask) {
                            const isAnotherOptimizationRunning = Object.values(activeTasks).some(t => t.type === 'optimize_text' || t.type === 'optimize_paragraph');
                            if (!isAnotherOptimizationRunning) {
                                setOptimizationState(false);
                                window.closeOptimizationModal();
                            }
                        }
                    }
                    continue;
                }

                // 记录轮询日志（仅对文档上传任务）
                if (task.type === 'document_upload' && DEBUG_POLLING) {
                    debugLog(`📊 轮询任务 ${taskId} 状态: ${data.status}`, 'debug');
                }

                const isFinished = data.status === 'completed' || data.status === 'failed';

                if (isFinished) {
                    if (data.status === 'completed') {
                        debugLog(`Task ${taskId} completed successfully. Type: ${task.type}`, 'debug');
                        // 删除：不再显示任务完成提示（用户看到结果就知道完成了）
                        if (task.type === 'document_upload') {
                            debugLog('Processing document_upload completion...', 'debug');

                            // Chrome特定处理：强制DOM更新
                            const isChrome = /Chrome/.test(navigator.userAgent) && !/Edge/.test(navigator.userAgent);

                            if (isChrome) {
                                // Chrome需要强制重绘来确保DOM更新
                                // Chrome特定处理：强制DOM更新（静默）
                                document.body.style.display = 'none';
                                document.body.offsetHeight; // 触发重排
                                document.body.style.display = '';
                            }

                            // 1. 立即显示内容（最重要，优先处理）
                            inputText.value = data.result.text;

                            // 2. 立即显示文档信息
                            fileNameSpan.textContent = `文件: ${data.result.fileName}`;
                            wordCountSpan.textContent = `字数: ${data.result.wordCount}`;
                            documentInfo.style.display = 'flex';

                            // 3. 立即切换到粘贴文本标签
                            const textTabButton = document.querySelector('.tab-button[data-tab="text"]');
                            const textTabPane = document.getElementById('textTabContent');
                            if (textTabButton && textTabPane) {
                                tabButtons.forEach(btn => btn.classList.remove('active'));
                                document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
                                textTabButton.classList.add('active');
                                textTabPane.classList.add('active');
                                currentTab = 'text';
                            }

                            // Chrome特定处理：延迟确保更新生效
                            if (isChrome) {
                                setTimeout(() => {
                                    // Chrome延迟确认（静默）
                                    if (inputText.value.length === 0 && data.result.text) {
                                        console.log('🔧 Chrome重试：重新设置文本框内容');
                                        inputText.value = data.result.text;
                                        inputText.dispatchEvent(new Event('input', { bubbles: true }));
                                    }
                                }, 100);
                            }

                            // 4. 异步处理其他操作（不阻塞显示）
                            setTimeout(() => {
                                currentDocument = {
                                    id: data.result.documentId,
                                    name: data.result.fileName,
                                    fileName: data.result.fileName, // 添加fileName字段，保持一致性
                                    content: data.result.text,
                                    paragraphs: data.result.paragraphs,
                                    uploadTime: new Date().toISOString()
                                };

                                window.currentDocument = currentDocument;
                                localStorage.setItem('currentDocument', JSON.stringify(currentDocument));
                                totalWordCount = data.result.wordCount;
                                updatePriceDisplay();
                            }, 0);

                        } else if (task.type === 'optimize_text' || task.type === 'optimize_paragraph') {
                            console.log('Processing optimize_text/optimize_paragraph completion...');

                            // 立即停止进度更新
                            window.stopProgressSimulation && window.stopProgressSimulation();

                            displayFinalResult(data.result.optimizedText);

                            // 优化完成后立即清除相关缓存数据（不依赖保存结果）
                            console.log('🧹 优化完成，清除相关缓存数据');
                            localStorage.removeItem('currentDocument');
                            localStorage.removeItem('pendingDocument');
                            localStorage.removeItem('pendingOptimizationContent');
                            localStorage.removeItem('pendingOptimizationFileName');
                            localStorage.removeItem('pendingOptimizationServiceType');
                            localStorage.removeItem('shouldRestoreTextContent');

                            // 获取文档名称用于保存
                            let documentNameForSave = '未命名文档';
                            if (currentDocument && currentDocument.fileName) {
                                documentNameForSave = currentDocument.fileName.replace(/\.[^/.]+$/, ""); // 移除扩展名
                            } else if (currentDocument && currentDocument.name) {
                                documentNameForSave = currentDocument.name.replace(/\.[^/.]+$/, ""); // 移除扩展名
                            } else {
                                documentNameForSave = '粘贴文本'; // 默认名称
                            }
                            console.log('📄 获取到的文档名称:', documentNameForSave);

                            // 定义清空状态的函数
                            const clearDocumentState = () => {
                                console.log('🧹 清空文档状态');

                                // 清除当前文档状态
                                currentDocument = null;
                                window.currentDocument = null;

                                // 清空粘贴文本框
                                const textarea = document.getElementById('inputText');
                                if (textarea) {
                                    textarea.value = '';
                                    console.log('📝 已清空粘贴文本框');
                                }

                                // 更新字数统计
                                totalWordCount = 0;
                                if (typeof updatePriceDisplay === 'function') {
                                    updatePriceDisplay();
                                }
                            };

                            // 自动保存优化结果到历史记录（在清理之后进行，避免依赖）
                            console.log('准备自动保存优化结果...');
                            console.log('优化后的文本:', data.result.optimizedText ? data.result.optimizedText.substring(0, 50) + '...' : '无内容');

                            try {
                                // 先保存优化结果，再清空
                                saveOptimizedDocument(data.result.optimizedText, documentNameForSave)
                                    .then(success => {
                                        if (success) {
                                            console.log('自动保存优化结果完成');
                                        } else {
                                            console.error('自动保存优化结果失败');
                                        }

                                        // 保存完成后（无论成功失败）都清空状态
                                        console.log('🧹 保存完成，开始清空状态');
                                        clearDocumentState();
                                    })
                                    .catch(err => {
                                        console.error('自动保存过程中出错:', err);

                                        // 即使保存出错也要清空状态
                                        console.log('🧹 保存出错，仍然清空状态');
                                        clearDocumentState();
                                    });
                            } catch (saveError) {
                                console.error('尝试自动保存时出错:', saveError);

                                // 如果连尝试保存都失败，也要清空状态
                                console.log('🧹 保存尝试失败，仍然清空状态');
                                clearDocumentState();
                            }
                        }
                    } else { // failed
                        showNotification(`${task.name} 失败: ${data.error}`, 'error');
                    }

                    const wasOptimizationTask = task.type === 'optimize_text' || task.type === 'optimize_paragraph';
                    delete activeTasks[taskId];

                    if (wasOptimizationTask) {
                        const isAnotherOptimizationRunning = Object.values(activeTasks).some(t => t.type === 'optimize_text' || t.type === 'optimize_paragraph');
                        if (!isAnotherOptimizationRunning) {
                            setOptimizationState(false); // Unlock UI
                            window.closeOptimizationModal(); // Close modal when all optimization tasks are done
                        }
                    }
                } else {
                    // 对于优化任务，只确保弹窗打开，不更新进度（由进度更新系统处理）
                    if (task.type === 'optimize_text' || task.type === 'optimize_paragraph') {
                        window.openOptimizationModal(); // Ensure modal is open

                        // 对于短文本（≤2000字），显示流式结果
                        if (data.segments && data.segments.length > 0) {
                            window.displayStreamingResults && window.displayStreamingResults(data.segments);
                        }
                    }
                }
            }
            catch (error) {
                console.error(`轮询任务 ${taskId} 失败:`, error);
                showNotification(`获取 ${task.name} 状态失败: ${error.message}`, 'error');

                // 清理失败的任务
                const taskToRemove = activeTasks[taskId];
                if (taskToRemove) {
                    const wasOptimizationTask = taskToRemove.type === 'optimize_text' || taskToRemove.type === 'optimize_paragraph';
                    delete activeTasks[taskId];
                    console.log(`已清理失败的任务: ${taskId}`);

                    if (wasOptimizationTask) {
                        const isAnotherOptimizationRunning = Object.values(activeTasks).some(t => t.type === 'optimize_text' || t.type === 'optimize_paragraph');
                        if (!isAnotherOptimizationRunning) {
                            setOptimizationState(false); // Unlock UI
                            window.closeOptimizationModal();
                            console.log('所有优化任务已清理，UI已解锁');
                        }
                    }
                }
            }
        }
    }

    // Poll every 2 seconds for faster response
    setInterval(pollTaskStatus, 2000);

    // 添加全局函数用于清理所有活动任务（调试用）
    window.clearAllActiveTasks = function() {
        console.log('清理所有活动任务:', Object.keys(activeTasks));
        Object.keys(activeTasks).forEach(taskId => {
            delete activeTasks[taskId];
        });
        setOptimizationState(false);
        window.closeOptimizationModal && window.closeOptimizationModal();
        console.log('所有活动任务已清理');
    };

    // 添加系统状态查询功能
    window.getSystemStatus = async function() {
        try {
            const response = await safeFetch(`${API_BASE_URL}/documents/system-status`);
            const data = await response.json();

            if (data.success) {
                return data.data;
            }
        } catch (error) {
            console.error('获取系统状态失败:', error);
        }
    };

    // 定期更新系统状态（可选）
    setInterval(async () => {
        if (Object.keys(activeTasks).length > 0) {
            const status = await window.getSystemStatus();
            if (status && status.system.status === 'busy') {
                console.log('⚠️ 系统繁忙，当前有多个用户在使用');
            }
        }
    }, 30000); // 每30秒检查一次

    // --- Event Listeners ---

    if (uploadBtn) {
        uploadBtn.addEventListener('click', (e) => {
            // 移除登录检查，允许未注册用户上传文档
            // if (!checkLoginStatus()) {
            //     e.preventDefault();
            //     requireLogin('上传文档');
            //     return false;
            // }
            if (documentUpload) {
                documentUpload.click();
            }
        });
    }

    if (documentUpload) {
        documentUpload.addEventListener('change', async (event) => {
            const file = event.target.files[0];
            if (!file) return;

            // 移除登录检查，允许未注册用户上传文档
            // if (!requireLogin('上传文档')) {
            //     // 清除文件选择
            //     event.target.value = '';
            //     return;
            // }

        try {
            const formData = new FormData();
            formData.append('document', file);

            console.log('📄 上传文件:', file.name, '大小:', file.size);
            debugLog('上传端点:', UPLOAD_API_ENDPOINT, 'debug');

            // 删除：不再显示上传过程提示
            uploadBtn.disabled = true;

            const response = await safeFetch(UPLOAD_API_ENDPOINT, {
                method: 'POST',
                headers: getAuthHeaders(true), // 使用专门的FormData头部
                body: formData
            });

            debugLog('上传响应状态:', response.status, 'debug');

            if (!response.ok) {
                const errorText = await response.text();
                console.error('上传失败响应:', errorText);
                try {
                    const errorData = JSON.parse(errorText);
                throw new Error(errorData.message || '文档上传失败');
                } catch (e) {
                    throw new Error(`上传失败 (${response.status}): ${errorText || '未知错误'}`);
            }
            }
            
            const data = await response.json();
            debugLog('上传响应数据:', data, 'debug');
            
            if (!data.success) {
                throw new Error(data.message || '文档处理失败');
            }

            activeTasks[data.taskId] = { name: `上传: ${file.name}`, type: 'document_upload' };

            // 不再需要轮询任务状态，直接使用响应数据
            // if (!pollInterval) {
            //     pollInterval = setInterval(pollTaskStatus, 2000);
            // }
        }
        catch (error) {
            console.error('上传错误详情:', error);
            showNotification('提交上传任务失败: ' + error.message, 'error');
        } finally {
            uploadBtn.disabled = false;
        }
        });
    }

    // 检查clearDocBtn是否存在（在历史记录页面中不存在）
    if (clearDocBtn) {
        clearDocBtn.addEventListener('click', () => {
            currentDocument = null;
            window.currentDocument = null;

            // 清除持久化存储
            localStorage.removeItem('currentDocument');
            localStorage.removeItem('pendingDocument');

        inputText.value = '';
        documentInfo.style.display = 'none';
        documentUpload.value = '';
        // 删除：不再显示文档清除提示
        // Clear output area as well
        outputText.innerHTML = '<p class="placeholder">优化后的文本将显示在这里...</p>';
            totalWordCount = 0; // Reset word count
            updatePriceDisplay(); // Update price display
        });
    }

    // 文本输入框焦点检查 - 移除登录限制
    if (inputText) {
        inputText.addEventListener('focus', (e) => {
            // 移除登录检查，允许未注册用户输入文本
            // if (!checkLoginStatus()) {
            //     e.preventDefault();
            //     inputText.blur();
            //     requireLogin('输入文本内容');
            //     return false;
            // }
        });

        inputText.addEventListener('input', () => {
            // 移除登录检查，允许未注册用户输入文本
            // if (inputText.value.length > 0 && !checkLoginStatus()) {
            //     inputText.value = '';
            //     requireLogin('输入文本内容');
            //     return;
            // }

            // 如果用户手动清空了文本框，清除相关的localStorage数据
            if (inputText.value.trim() === '') {
                console.log('📝 用户清空了文本框，清除相关缓存');
                localStorage.removeItem('currentDocument');
                localStorage.removeItem('pendingDocument');
                localStorage.removeItem('pendingOptimizationContent');
                localStorage.removeItem('shouldRestoreTextContent');
                currentDocument = null;
                window.currentDocument = null;
            }

            totalWordCount = inputText.value.length;
            if (wordCountSpan) wordCountSpan.textContent = `字数: ${totalWordCount}`;
            updatePriceDisplay();
        });
    }

    // 检查服务卡片元素是否存在（在历史记录页面中不存在）
    if (aiServiceCard) {
        aiServiceCard.addEventListener('click', () => {
            selectedService = 'ai';
            updatePriceDisplay();
        });
    }

    if (expertServiceCard) {
        expertServiceCard.addEventListener('click', () => {
            // 检查学术专家服务是否需要登录
            if (!checkLoginStatus()) {
                showExpertServiceLoginModal();
                return;
            }
            selectedService = 'expert';
            updatePriceDisplay();
        });
    }

    // 注释掉重复的支付按钮事件处理器，避免与第1553行的处理器冲突
    // document.querySelectorAll('.pay-now-btn-card').forEach(button => {
    //     button.addEventListener('click', (e) => {
    //         e.stopPropagation(); // Prevent card selection click
    //         const service = e.target.dataset.service;
    //         if (totalWordCount > 0) {
    //             openPaymentModal(service);
    //         } else {
    //             showNotification('请先上传或输入内容', 'info');
    //         }
    //     });
    // });

    if (submitBtn) {
        submitBtn.addEventListener('click', async (e) => {
            // 检查是否为免费优化用户
            if (isFreeOptimizeUser()) {
                console.log('🎁 免费优化用户，直接开始优化');
                // 免费用户直接开始优化，跳过支付验证
                startDirectOptimization();
                return;
            }

            // 普通用户的原有逻辑（实际上这部分代码不应该被执行，因为按钮对普通用户是隐藏的）
            showNotification('请通过支付后自动优化', 'info');
            return;

            let text = '';
            let endpoint = '';
            let taskName = '';
            let taskType = '';
            let bodyData = {};

        if (currentTab === 'upload') {
            if (!currentDocument) {
                showNotification('请先上传文档', 'info');
                return;
            }
            if (!currentDocument.id) {
                showNotification('文档ID缺失，请尝试重新上传文档或直接输入文本。', 'error');
                setOptimizationState(false);
                return;
            }
            text = currentDocument.content;
            endpoint = PARAGRAPH_API_ENDPOINT;
            taskName = `文档优化: ${currentDocument.name}`;
            taskType = 'optimize_paragraph';
            bodyData = { text: text, type: optimizeType.value, documentId: currentDocument.id };
        } else { // currentTab === 'text'
            text = inputText.value.trim();
            if (!text) {
                showNotification('请输入需要优化的文本', 'info');
                return;
            }
            endpoint = OPTIMIZE_API_ENDPOINT;
            taskName = '文本优化';
            taskType = 'optimize_text';
            bodyData = { text: text, type: optimizeType.value };
        }

        const headers = getAuthHeaders();
        // headers现在总是返回有效值（登录用户或临时用户）

        setOptimizationState(true); // Lock UI before request

        try {
            const response = await safeFetch(endpoint, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(bodyData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '提交优化任务失败');
            }
            const data = await response.json();
            if (!data.success) throw new Error(data.message || '提交优化任务失败');

            activeTasks[data.taskId] = { name: taskName, type: taskType };
            // 删除：不再显示任务提交提示

        }
        catch (error) {
            showNotification('提交优化任务失败: ' + error.message, 'error');
            setOptimizationState(false); // Unlock UI on submission failure
        }
        });
    }

    // 检查是否为免费优化用户
    function isFreeOptimizeUser() {
        const token = localStorage.getItem('token');
        if (token) {
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                const userId = payload.userId;
                // qaa用户的ID是9
                return userId === 9;
            } catch (error) {
                console.log('Token解析失败:', error);
                return false;
            }
        }
        return false;
    }

    // 检查并显示免费优化按钮
    function checkFreeOptimizePermission() {
        if (isFreeOptimizeUser()) {
            const submitBtn = document.getElementById('submitBtn');
            if (submitBtn) {
                submitBtn.style.display = 'inline-block';
                console.log('🎁 检测到免费优化用户，显示立即优化按钮');
            }
        }
    }

    // 免费用户直接开始优化
    function startDirectOptimization() {
        console.log('🚀 免费用户直接开始优化');

        // 检查内容
        let text = '';
        let documentName = '';

        if (currentTab === 'upload') {
            if (!currentDocument) {
                showNotification('请先上传文档', 'info');
                return;
            }
            text = currentDocument.content;
            documentName = currentDocument.name;
        } else {
            text = inputText.value.trim();
            if (!text) {
                showNotification('请输入需要优化的文本', 'info');
                return;
            }
            documentName = '文本优化';
        }

        // 显示优化弹窗
        if (window.openOptimizationModal) {
            window.openOptimizationModal();
        }

        // 直接开始优化
        const optimizeType = document.getElementById('optimizeType');
        startOptimization(text, optimizeType.value, documentName);
    }

    // 检查支付状态并开始优化
    async function checkPaymentStatusAndStartOptimization() {
        console.log('🔍 检查支付状态...');

        try {
            const headers = getAuthHeaders();
            // headers现在总是返回有效值（登录用户或临时用户）

            // 获取最后一个支付ID
            const lastPaymentId = localStorage.getItem('lastPaymentId');
            if (!lastPaymentId) {
                console.error('❌ 没有找到支付ID');
                showNotification('没有找到支付信息', 'error');
                return;
            }

            // 检查支付状态
            const response = await fetch(`${API_BASE_URL}/payments/status/${lastPaymentId}`, {
                method: 'GET',
                headers: headers
            });

            if (!response.ok) {
                throw new Error('检查支付状态失败');
            }

            const data = await response.json();
            console.log('💰 支付状态:', data);

            if (data.success && data.status === 'paid') {
                console.log('✅ 支付成功，开始自动优化');
                // 删除：不再显示支付成功提示（后续会有验证成功提示）

                // 延迟1秒后开始优化，让用户看到成功提示
                setTimeout(() => {
                    startAutoOptimization();
                }, 1000);
            } else {
                console.log('⏳ 支付未完成或失败');
                showNotification('支付未完成，如已支付请稍后重试', 'warning');
            }

        } catch (error) {
            console.error('❌ 检查支付状态失败:', error);
            showNotification('检查支付状态失败: ' + error.message, 'error');
        }
    }

    // 自动开始优化
    function startAutoOptimization() {
        console.log('🚀 开始自动优化');

        // 调试信息：检查各种可能的内容来源
        const textarea = document.getElementById('inputText');
        console.log('📝 textarea元素:', textarea ? '存在' : '不存在');
        console.log('📝 textarea值长度:', textarea ? textarea.value.length : 0);
        console.log('📝 textarea值预览:', textarea ? textarea.value.substring(0, 100) + '...' : '无');

        console.log('📄 currentDocument:', currentDocument);
        console.log('📄 window.currentDocument:', window.currentDocument);

        // 尝试从多个来源获取文档内容
        let text = '';
        let documentName = '文档优化';

        // 1. 优先从textarea获取
        if (textarea && textarea.value.trim()) {
            text = textarea.value.trim();
            console.log('✅ 从textarea获取内容，长度:', text.length);
        }
        // 2. 从currentDocument获取
        else if (currentDocument && currentDocument.content) {
            text = currentDocument.content.trim();
            documentName = currentDocument.name || documentName;
            console.log('✅ 从currentDocument获取内容，长度:', text.length);
        }
        // 3. 从window.currentDocument获取
        else if (window.currentDocument && window.currentDocument.content) {
            text = window.currentDocument.content.trim();
            documentName = window.currentDocument.name || documentName;
            console.log('✅ 从window.currentDocument获取内容，长度:', text.length);
        }
        // 4. 从localStorage获取
        else {
            try {
                const savedDoc = localStorage.getItem('currentDocument');
                if (savedDoc) {
                    const docData = JSON.parse(savedDoc);
                    if (docData.content) {
                        text = docData.content.trim();
                        documentName = docData.name || documentName;
                        console.log('✅ 从localStorage获取内容，长度:', text.length);
                    }
                }
            } catch (error) {
                console.error('解析localStorage文档失败:', error);
            }
        }

        if (!text) {
            console.error('❌ 没有找到文档内容可以优化');
            console.error('调试信息 - textarea:', textarea?.value?.length || 0);
            console.error('调试信息 - currentDocument:', currentDocument?.content?.length || 0);
            console.error('调试信息 - window.currentDocument:', window.currentDocument?.content?.length || 0);

            if (typeof showNotification === 'function') {
                showNotification('没有文档内容可以优化', 'error');
            } else {
                alert('没有文档内容可以优化');
            }
            return;
        }

        console.log('🎯 准备优化文档:', documentName, '内容长度:', text.length);

        const optimizeType = 'academic'; // 默认优化类型

        // 显示优化弹窗
        if (window.openOptimizationModal) {
            window.openOptimizationModal();
        }

        // 开始优化
        startOptimization(text, optimizeType, documentName);
    }

    // 独立的优化函数，用于自动触发优化
    async function startOptimization(text, optimizeType, documentName) {
        console.log('🚀 开始执行优化，文档:', documentName);

        const headers = getAuthHeaders();
        // headers现在总是返回有效值（登录用户或临时用户）

        // 设置优化状态
        setOptimizationState(true);

        try {
            // 使用文档优化API
            const endpoint = PARAGRAPH_API_ENDPOINT;
            const taskName = `文档优化: ${documentName}`;
            const taskType = 'optimize_paragraph';
            const bodyData = {
                text: text,
                type: optimizeType,
                documentId: currentDocument.id
            };

            debugLog('📤 发送优化请求到:', endpoint, 'debug');

            const response = await safeFetch(endpoint, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(bodyData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '提交优化任务失败');
            }

            const data = await response.json();
            if (!data.success) throw new Error(data.message || '提交优化任务失败');

            // 添加到活动任务列表
            activeTasks[data.taskId] = { name: taskName, type: taskType };

            console.log('✅ 优化任务已提交，任务ID:', data.taskId);
            // 删除：不再显示任务提交提示

            // 启动进度更新
            window.startProgressSimulation(data.taskId, text);

        } catch (error) {
            console.error('❌ 优化任务提交失败:', error);
            showNotification('提交优化任务失败: ' + error.message, 'error');
            setOptimizationState(false);
        }
    }

    function displayFinalResult(resultText) {
        const wordCount = resultText.replace(/\s+/g, '').length;
        const paragraphs = resultText.split(/\n\s*\n/).filter(p => p.trim().length > 0);
        const paragraphCount = paragraphs.length;

        const formattedResult = paragraphs.map(p => `<p>${p.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</p>`).join('');

        // Update the outputText box with just the text content
        outputText.innerHTML = `<pre>${formattedResult}</pre>`;

        // 🔧 为未登录用户保存最近优化记录（72小时有效）
        const isLoggedIn = localStorage.getItem('token');
        if (!isLoggedIn) {
            try {
                const originalFileName = currentDocument ? currentDocument.name : '粘贴文本';
                const nameWithoutExt = originalFileName.replace(/\.[^/.]+$/, '');
                const optimizedFileName = `${nameWithoutExt}+优化版.docx`;

                const expireTime = Date.now() + (72 * 60 * 60 * 1000); // 72小时后过期

                // 获取现有记录并清理过期的
                let recentOpts = JSON.parse(localStorage.getItem('recentOptimizations') || '[]');
                recentOpts = recentOpts.filter(item => item.expireTime > Date.now());

                // 添加新记录
                const docInfo = {
                    fileName: optimizedFileName,
                    originalName: originalFileName,
                    serviceType: 'ai_optimization',
                    status: 'completed',
                    content: resultText,
                    wordCount: wordCount,
                    paragraphCount: paragraphCount,
                    timestamp: Date.now(),
                    expireTime: expireTime,
                    id: 'recent_' + Date.now()
                };

                recentOpts.unshift(docInfo);

                // 最多保存10个记录
                if (recentOpts.length > 10) {
                    recentOpts = recentOpts.slice(0, 10);
                }

                localStorage.setItem('recentOptimizations', JSON.stringify(recentOpts));
                console.log('✅ 最近优化记录已保存（72小时有效）');
            } catch (error) {
                console.error('❌ 保存最近优化记录失败:', error);
            }
        }

        // Find the parent .output-section card
        const outputSection = outputText.closest('.output-section');
        if (!outputSection) return;

        // Remove any existing action buttons to prevent duplicates
        const existingActions = outputSection.querySelector('.result-actions');
        if (existingActions) {
            existingActions.remove();
        }

        // 检查是否为临时用户且未填写邮箱
        const userLoggedIn = localStorage.getItem('token');
        const shouldShowEmailCollection = !userLoggedIn && !localStorage.getItem('userEmailCollected');

        // Create and append the new action buttons container
        const actionsContainer = document.createElement('div');
        actionsContainer.className = 'result-actions';

        let emailCollectionHtml = '';
        if (shouldShowEmailCollection) {
            emailCollectionHtml = `
                <div class="apple-completion-card" style="
                    margin-bottom: 24px;
                    padding: 28px;
                    background: rgba(255, 255, 255, 0.85);
                    backdrop-filter: blur(20px);
                    -webkit-backdrop-filter: blur(20px);
                    border-radius: 20px;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05);
                ">
                    <div class="completion-header" style="
                        display: flex;
                        align-items: center;
                        margin-bottom: 20px;
                    ">
                        <div class="completion-icon" style="
                            width: 52px;
                            height: 52px;
                            background: linear-gradient(135deg, #007AFF, #5856D6);
                            border-radius: 16px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-right: 16px;
                            box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
                        ">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2.5">
                                <path d="M20 6L9 17l-5-5"/>
                            </svg>
                        </div>
                        <div class="completion-content">
                            <h3 style="
                                margin: 0 0 6px 0;
                                color: #1D1D1F;
                                font-size: 20px;
                                font-weight: 600;
                                letter-spacing: -0.5px;
                            ">🎉 优化完成！</h3>
                            <p style="
                                margin: 0;
                                color: #86868B;
                                font-size: 15px;
                                font-weight: 400;
                                line-height: 1.4;
                            ">保存到云端，随时随地访问您的历史记录</p>
                        </div>
                    </div>

                    <div class="apple-save-form" style="
                        display: flex;
                        gap: 12px;
                        align-items: center;
                    ">
                        <input type="email" id="completionEmail" placeholder="输入邮箱地址（可选）" style="
                            flex: 1;
                            padding: 16px 20px;
                            border: 1px solid rgba(0, 0, 0, 0.1);
                            border-radius: 12px;
                            font-size: 16px;
                            background: rgba(255, 255, 255, 0.8);
                            backdrop-filter: blur(10px);
                            -webkit-backdrop-filter: blur(10px);
                            transition: all 0.3s ease;
                            outline: none;
                        " onfocus="this.style.borderColor='#007AFF'; this.style.boxShadow='0 0 0 4px rgba(0, 122, 255, 0.1)'"
                           onblur="this.style.borderColor='rgba(0, 0, 0, 0.1)'; this.style.boxShadow='none'">
                        <button id="saveToCloudBtn" style="
                            padding: 16px 28px;
                            background: linear-gradient(135deg, #007AFF, #5856D6);
                            color: white;
                            border: none;
                            border-radius: 12px;
                            font-size: 16px;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
                            white-space: nowrap;
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(0, 122, 255, 0.4)'"
                           onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 16px rgba(0, 122, 255, 0.3)'">保存到云端</button>
                    </div>
                </div>
            `;
        }

        actionsContainer.innerHTML = `
            ${emailCollectionHtml}
            <div class="result-stats">
                <div class="stat-item">总字数: ${wordCount}</div>
                <div class="stat-item">段落数: ${paragraphCount}</div>
            </div>
            <div>
                <button id="copyBtn" class="primary-btn">复制结果</button>
                <button id="downloadOptimizedBtn" class="primary-btn">下载优化文档</button>
            </div>
        `;

        outputSection.appendChild(actionsContainer);

        // Add event listeners to the new buttons
        outputSection.querySelector('#copyBtn').addEventListener('click', copyOutput);
        outputSection.querySelector('#downloadOptimizedBtn').addEventListener('click', () => downloadOptimizedDocument(resultText, currentDocument ? currentDocument.name : null));

        // Add event listeners for email collection (if present)
        if (shouldShowEmailCollection) {
            const saveToCloudBtn = outputSection.querySelector('#saveToCloudBtn');
            const completionEmailInput = outputSection.querySelector('#completionEmail');

            if (saveToCloudBtn) {
                saveToCloudBtn.addEventListener('click', async () => {
                    const email = completionEmailInput.value.trim();
                    if (!email) {
                        showNotification('请输入邮箱地址', 'warning');
                        return;
                    }
                    if (!isValidEmail(email)) {
                        showNotification('请输入有效的邮箱地址', 'warning');
                        return;
                    }

                    try {
                        // 保存邮箱到临时用户记录
                        await saveEmailToTempUser(email);
                        localStorage.setItem('userEmailCollected', 'true');
                        showNotification('邮箱已保存，您可以通过邮箱访问历史记录', 'success');

                        // 隐藏邮箱收集区域
                        const emailCollection = outputSection.querySelector('.apple-completion-card');
                        if (emailCollection) {
                            emailCollection.style.display = 'none';
                        }
                    } catch (error) {
                        console.error('保存邮箱失败:', error);
                        showNotification('保存邮箱失败，请稍后重试', 'error');
                    }
                });
            }
        }
    }

    async function downloadOptimizedDocument(content, originalFileName) {
        const headers = getAuthHeaders();
        // headers现在总是返回有效值（登录用户或临时用户）

        try {
            // 删除：不再显示下载准备提示
            
            console.log('发送请求到:', `${API_BASE_URL}/documents/save-optimized`);
            console.log('请求数据:', { 
                content: content.substring(0, 50) + '...', // 只显示内容的前50个字符
                fileName: originalFileName,
                documentId: currentDocument ? currentDocument.id : null
            });
            
            const response = await fetch(`${API_BASE_URL}/documents/save-optimized`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({ 
                    content: content, 
                    fileName: originalFileName,
                    documentId: currentDocument ? currentDocument.id : null  // 传递当前文档ID
                })
            });

            console.log('响应状态:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('下载失败响应:', errorText);
                try {
                    const errorData = JSON.parse(errorText);
                throw new Error(errorData.message || '生成DOCX文件失败');
                } catch (e) {
                    throw new Error(`下载失败 (${response.status}): ${errorText || '未知错误'}`);
                }
            }

            const blob = await response.blob();
            const filename = `${originalFileName ? originalFileName.split('.')[0] : 'optimized_document'}_优化版.docx`;
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            showNotification('优化文档已保存并开始下载！', 'success');

        }
        catch (error) {
            console.error('下载DOCX文件失败:', error);
            showNotification('下载DOCX文件失败: ' + error.message, 'error');
        }
    }

    function copyOutput() {
        const preElement = outputText.querySelector('pre');
        if (!preElement) {
            showNotification('没有可复制的内容', 'info');
            return;
        }

        // 获取纯文本内容，去除HTML标签
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = preElement.innerHTML;
        const textToCopy = tempDiv.textContent || tempDiv.innerText || '';

        if (!textToCopy.trim()) {
            showNotification('没有可复制的内容', 'info');
            return;
        }

        navigator.clipboard.writeText(textToCopy).then(() => {
            const copyBtn = document.querySelector('#copyBtn');
            if (copyBtn) {
                copyBtn.textContent = '✅ 已复制！';
                setTimeout(() => { copyBtn.textContent = '复制结果'; }, 2000);
            }
            // 删除：不再显示复制成功提示（按钮变化已提供视觉反馈）
        }).catch(err => {
            showNotification('复制失败，请手动复制', 'error');
            console.error('Copy failed:', err);
        });
    }

    // Initial UI update on page load
    updateUIBasedOnAuthState().catch(console.error);
    updatePriceDisplay();
    updateModalQrCodeLogo(); // Initialize QR code logo

    // 监听localStorage变化，实时更新UI状态
    window.addEventListener('storage', (e) => {
        if (e.key === 'token') {
            console.log('Token状态发生变化:', e.oldValue ? '有' : '无', '->', e.newValue ? '有' : '无');
            // 同步AuthManager的状态
            window.AuthManager._token = e.newValue;
            updateUIBasedOnAuthState().catch(console.error);
        }
    });

    // 定期检查token状态（防止同页面内的token变化）
    setInterval(() => {
        const isLoggedIn = window.AuthManager.isLoggedIn();
        const uiShowsLoggedIn = userMenu && userMenu.style.display !== 'none';

        if (isLoggedIn !== uiShowsLoggedIn) {
            console.log('检测到token状态与UI不一致，更新UI');
            updateUIBasedOnAuthState().catch(console.error);
        }
    }, 1000);

    // 联系客服弹窗逻辑
    const contactBtn = document.getElementById('contactServiceBtn');
    const footerContactBtn = document.getElementById('footerContactUsBtn');

    if (contactBtn && contactModal && closeContactModal) {
        // 打开弹窗
        contactBtn.addEventListener('click', (e) => {
            e.preventDefault();
            showContactModal();
        });

        // 关闭弹窗
        closeContactModal.addEventListener('click', () => {
            hideContactModal();
        });

        // 点击弹窗外部关闭
        contactModal.addEventListener('click', (e) => {
            if (e.target === contactModal) {
                hideContactModal();
            }
        });

        // 阻止弹窗内部点击事件冒泡
        contactModal.querySelector('.contact-modal-content').addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }

    // 为页脚的“联系我们”按钮添加事件监听
    if (footerContactBtn) {
        footerContactBtn.addEventListener('click', (e) => {
            e.preventDefault();
            showContactModal();
        });
    }

    // 在文档加载完成后添加支付宝直接支付按钮的事件监听
    document.addEventListener('DOMContentLoaded', () => {
        // 添加支付宝直接支付按钮的点击事件处理
        const alipayDirectBtn = document.getElementById('alipayDirectBtn');
        if (alipayDirectBtn) {
            alipayDirectBtn.addEventListener('click', async function(e) {
                e.preventDefault();
                
                // 保存待优化的内容到localStorage
                const contentToOptimize = currentDocument ? currentDocument.content : inputText.value;
                localStorage.setItem('pendingOptimization', JSON.stringify({
                    content: contentToOptimize,
                    serviceType: selectedService,
                    timestamp: Date.now()
                }));
                console.log('已保存待优化内容到localStorage');

                // 保存选择的服务类型，用于支付成功后的处理
                localStorage.setItem('lastSelectedService', selectedService);
                console.log('已保存服务类型:', selectedService);

                // 创建支付宝订单
                try {
                    // 防重复点击检查
                    if (alipayDirectBtn.disabled) {
                        console.log('⚠️ 支付正在处理中，忽略重复点击');
                        return;
                    }

                    // 显示加载状态
                    alipayDirectBtn.disabled = true;
                    alipayDirectBtn.textContent = '处理中...';

                    const price = calculatePrice(totalWordCount, selectedService);
                    const response = await fetch(`${API_BASE_URL}/payments/alipay/create`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('token')}`
                        },
                        body: JSON.stringify({
                            amount: price,
                            description: selectedService === 'ai' ? 'WriterPro内容优化' : '专家润色服务',
                            serviceType: selectedService,
                            content: getCurrentContent() // 添加内容信息
                        })
                    });

                    if (!response.ok) {
                        throw new Error('创建订单失败');
                    }

                    const result = await response.text();
                    console.log('获取到支付表单:', result.substring(0, 100) + '...');
                    
                    // 创建一个临时div来解析表单
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = result;
                    const form = tempDiv.querySelector('form');
                    
                    if (form) {
                        // 提取表单属性
                        const formAction = form.getAttribute('action');
                        const formMethod = form.getAttribute('method') || 'POST';
                        const formInputs = form.querySelectorAll('input');
                        
                        // 创建新的表单
                        const newForm = document.createElement('form');
                        newForm.setAttribute('action', formAction);
                        newForm.setAttribute('method', formMethod);
                        newForm.setAttribute('target', '_self'); // 在当前页面打开
                        newForm.style.display = 'none';
                        
                        // 复制所有输入字段
                        formInputs.forEach(input => {
                            const newInput = document.createElement('input');
                            newInput.setAttribute('type', input.getAttribute('type') || 'hidden');
                            newInput.setAttribute('name', input.getAttribute('name') || '');
                            newInput.setAttribute('value', input.getAttribute('value') || '');
                            newForm.appendChild(newInput);
                        });
                        
                        // 添加到页面并提交
                        document.body.appendChild(newForm);
                        
                        // 关闭支付模态框
                        if (typeof closePaymentModal === 'function') {
                            closePaymentModal();
                        }

                        // 🚀 启用智能支付：根据弹窗支持情况选择支付方式
                        console.log('🧪 开始智能支付检测（第二处）...');
                        const popupSupported = checkPopupSupportRealtime();
                        console.log('弹窗支持检测结果:', popupSupported);

                        if (popupSupported) {
                            // 弹窗支持，尝试弹窗支付
                            console.log('✅ 弹窗支持，使用弹窗支付');
                            // 删除：不再显示打开支付窗口提示

                            try {
                                // 根据设备类型处理支付跳转
                                if (isMobileDevice()) {
                                    // 移动设备：直接提交表单，会自动调用支付宝客户端
                                    console.log('📱 移动设备检测到，直接提交支付表单');
                                    newForm.target = '_self'; // 在当前页面提交
                                    newForm.submit();
                                    return;
                                }

                                // 桌面设备：使用弹窗
                                const paymentWindow = window.open('', 'alipayPayment', 'width=800,height=600,scrollbars=yes,resizable=yes');

                                if (paymentWindow && !paymentWindow.closed) {
                                    // 弹窗成功打开
                                    console.log('✅ 支付窗口已打开');
                                    // 删除：不再显示支付窗口已打开提示

                                    // 监听支付窗口关闭
                                    const checkClosed = setInterval(() => {
                                        if (paymentWindow.closed) {
                                            clearInterval(checkClosed);
                                            console.log('支付窗口已关闭');
                                            // 删除：不再显示验证支付状态提示
                                        }
                                    }, 1000);

                                    // 提交表单到弹窗
                                    newForm.target = 'alipayPayment';
                                    setTimeout(() => {
                                        newForm.submit();
                                        console.log('表单已提交到支付窗口');
                                    }, 100);

                                } else {
                                    // 弹窗被阻止，使用备用方案
                                    console.log('❌ 弹窗被阻止，启用备用支付方案');
                                    showPaymentFallbackModal(newForm, { paymentUrl: newForm.action });
                                }
                            } catch (popupError) {
                                console.error('弹窗支付失败:', popupError);
                                console.log('❌ 弹窗支付异常，启用备用支付方案');
                                showPaymentFallbackModal(newForm, { paymentUrl: newForm.action });
                            }
                        } else {
                            // 弹窗不支持，直接使用备用方案
                            console.log('❌ 浏览器不支持弹窗，启用备用支付方案');
                            showPaymentFallbackModal(newForm, { paymentUrl: newForm.action });
                        }
                    } else {
                        // 如果无法解析表单，直接设置HTML内容
                        document.open();
                        document.write(result);
                        document.close();
                        
                        // 尝试自动提交
                        setTimeout(() => {
                            const autoForm = document.querySelector('form');
                            if (autoForm) {
                                try {
                                    autoForm.submit();
                                } catch (e) {
                                    console.error('自动提交失败:', e);
                                    alert('支付表单加载失败，请刷新页面重试');
                                }
                            }
                        }, 500);
                    }
                } catch (error) {
                    console.error('创建支付宝订单失败:', error);
                    if (typeof showNotification === 'function') {
                        showNotification('创建订单失败，请重试', 'error');
                    } else {
                        alert('创建订单失败，请重试');
                    }
                    
                    // 恢复按钮状态
                    alipayDirectBtn.disabled = false;
                    alipayDirectBtn.innerHTML = `
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="vertical-align: middle; margin-right: 8px;">
                          <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z" fill="#00A0E9"/>
                          <path d="M15.5 8.5h-7c-.552 0-1 .448-1 1v5c0 .552.448 1 1 1h7c.552 0 1-.448 1-1v-5c0-.552-.448-1-1-1z" fill="#fff"/>
                          <path d="M12 10.5c-.828 0-1.5.672-1.5 1.5s.672 1.5 1.5 1.5 1.5-.672 1.5-1.5-.672-1.5-1.5-1.5z" fill="#00A0E9"/>
                        </svg>
                        支付宝支付 ¥<span class="alipay-amount">${calculatePrice(totalWordCount, selectedService)}</span>
                    `;
                }
            });
        }
    });
});

// --- 进度模拟系统 ---
// 处理历史记录
const processingHistory = JSON.parse(localStorage.getItem('processingHistory') || '[]');

// 根据文本特征估算处理信息
function estimateProcessingInfo(text) {
    const textLength = text.length;
    const paragraphCount = Math.max(1, text.split(/\n\s*\n/).filter(p => p.trim().length > 0).length);
    const sentenceCount = text.split(/[。！？；]/).filter(s => s.trim().length > 0).length;

    // 基于历史数据的改进估算
    if (processingHistory.length >= 3) {
        const avgTimePerChar = processingHistory.reduce((sum, record) =>
            sum + (record.actualTime / record.textLength), 0) / processingHistory.length;

        const estimatedTime = Math.max(10, 15 + (textLength * avgTimePerChar));
        const virtualSegments = Math.max(5, Math.min(20, Math.ceil(textLength / 200)));

        return {
            estimatedTime: Math.round(estimatedTime),
            virtualSegments,
            avgTimePerSegment: estimatedTime / virtualSegments
        };
    }

    // 默认估算（首次使用）
    const baseTime = 15; // 基础时间15秒
    const timePerChar = 0.025; // 每字符0.025秒
    const estimatedTime = Math.max(10, baseTime + (textLength * timePerChar));

    // 估算虚拟段落数
    const virtualSegments = Math.max(5, Math.min(20, Math.ceil(textLength / 200)));

    return {
        estimatedTime: Math.round(estimatedTime),
        virtualSegments,
        avgTimePerSegment: estimatedTime / virtualSegments
    };
}

// 更新处理历史记录
function updateProcessingHistory(textLength, actualTime) {
    processingHistory.push({
        textLength,
        actualTime,
        timestamp: Date.now()
    });

    // 只保留最近20条记录
    if (processingHistory.length > 20) {
        processingHistory.shift();
    }

    localStorage.setItem('processingHistory', JSON.stringify(processingHistory));
    console.log(`📊 更新处理历史: ${textLength}字符, ${actualTime}秒, 历史记录数: ${processingHistory.length}`);
}

// --- Modal Logic ---
document.addEventListener('DOMContentLoaded', () => {
    const modal = document.getElementById('optimizationModal');

    // 如果modal不存在（比如在历史记录页面），则跳过初始化
    if (!modal) {
        console.log('优化模态框不存在，跳过初始化');
        return;
    }

    const progressPercent = document.getElementById('progressPercent');
    const progressBarFill = modal.querySelector('.progress-bar-fill'); // New element
    const progressStatus = document.getElementById('progressStatus');

    // 全局变量存储进度更新状态
    let currentProgressInterval = null;
    let progressStartTime = null;
    let currentTaskInfo = null;

    // Function to open the modal
    window.openOptimizationModal = () => {
        // 只在弹窗未打开时才重置进度
        const isModalOpen = modal.style.display === 'flex';

        modal.style.display = 'flex';

        // 只在首次打开时重置进度，避免干扰进度更新
        if (!isModalOpen) {
            progressPercent.textContent = '0%';
            progressBarFill.style.width = '0%';
            progressStatus.textContent = '准备开始优化...';
        }
    };

    // 启动进度更新
    window.startProgressSimulation = (taskId, text) => {
        debugLog(`🎬 启动进度更新，任务ID: ${taskId}, 文本长度: ${text.length}`, 'debug');

        // 清理之前的定时器
        if (currentProgressInterval) clearInterval(currentProgressInterval);

        const estimateInfo = estimateProcessingInfo(text);
        progressStartTime = Date.now();
        currentTaskInfo = {
            taskId,
            textLength: text.length,
            ...estimateInfo
        };

        console.log(`📊 估算信息:`, estimateInfo);

        let currentSegment = 0;

        // 进度更新定时器
        currentProgressInterval = setInterval(() => {
            const elapsed = (Date.now() - progressStartTime) / 1000;
            const expectedProgress = elapsed / estimateInfo.avgTimePerSegment;

            // 添加随机波动，模拟真实处理
            const randomFactor = 0.85 + Math.random() * 0.3; // 0.85-1.15倍速度
            const adjustedProgress = expectedProgress * randomFactor;
            const targetSegment = Math.min(Math.floor(adjustedProgress), estimateInfo.virtualSegments - 1);

            if (targetSegment > currentSegment) {
                currentSegment = targetSegment;
                const percentage = Math.min(Math.round((currentSegment / estimateInfo.virtualSegments) * 95), 95);

                // 更新进度显示（静默）
                window.updateOptimizationProgress(
                    currentSegment,
                    estimateInfo.virtualSegments,
                    'processing',
                    '正在优化文档...',
                    percentage
                );
            }

            // 超时保护
            if (elapsed >= estimateInfo.estimatedTime * 1.5) {
                debugLog('⏰ 进度更新超时，停止更新', 'debug');
                clearInterval(currentProgressInterval);
            }
        }, 1500); // 每1.5秒更新一次

        // 依靠主轮询检测完成状态，不需要额外检查
    };





    // 停止进度更新的全局函数
    window.stopProgressSimulation = function() {
        debugLog('🛑 停止进度更新', 'debug');
        if (currentProgressInterval) {
            clearInterval(currentProgressInterval);
            currentProgressInterval = null;
        }

        // 瞬间跳到100%
        if (currentTaskInfo) {
            window.updateOptimizationProgress(
                currentTaskInfo.virtualSegments,
                currentTaskInfo.virtualSegments,
                'completed',
                '优化完成！',
                100
            );
        }

        // 1秒后关闭弹窗
        setTimeout(() => {
            window.closeOptimizationModal();
        }, 1000);
    };

    // Function to close the modal
    window.closeOptimizationModal = () => {
        modal.style.display = 'none';
    };

    // Function to update progress with enhanced display
    window.updateOptimizationProgress = (completed, total, status, detail, percentage, textLength = 0) => {
        const percent = percentage || (total > 0 ? Math.round((completed / total) * 100) : 0);
        progressPercent.textContent = `${percent}%`;
        progressBarFill.style.width = `${percent}%`;

        // 只显示简单的状态信息
        progressStatus.textContent = '正在优化文档...';
    };

    // Function to display streaming results (for texts ≤2000 characters)
    window.displayStreamingResults = (segments, completedParagraphs, totalParagraphs) => {
        debugLog(`🔄 流式显示：已完成${completedParagraphs}段，共${totalParagraphs}段`, 'debug');

        let streamingContainer = modal.querySelector('.streaming-results');
        if (!streamingContainer) {
            // 创建流式结果容器
            streamingContainer = document.createElement('div');
            streamingContainer.className = 'streaming-results';
            streamingContainer.style.cssText = `
                margin-top: 20px;
                max-height: 300px;
                overflow-y: auto;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 15px;
                background: #f9f9f9;
            `;

            // 插入到进度条下方
            const progressContainer = modal.querySelector('.progress-container') || modal.querySelector('.modal-content');
            if (progressContainer) {
                progressContainer.appendChild(streamingContainer);
            }
        }

        // 更新流式结果内容
        let html = '<h4 style="margin: 0 0 15px 0; color: #333;">实时优化结果：</h4>';

        segments.forEach((segment, index) => {
            const paragraphNumber = index + 1;
            html += `
                <div style="margin-bottom: 15px; padding: 10px; background: white; border-radius: 5px; border-left: 3px solid #4CAF50;">
                    <div style="font-weight: bold; color: #4CAF50; margin-bottom: 5px;">
                        ✅ 第${paragraphNumber}段已完成
                    </div>
                    <div style="color: #666; font-size: 14px; line-height: 1.5;">
                        ${segment.substring(0, 200)}${segment.length > 200 ? '...' : ''}
                    </div>
                </div>
            `;
        });

        // 显示正在处理的段落
        if (completedParagraphs < totalParagraphs) {
            html += `
                <div style="margin-bottom: 15px; padding: 10px; background: #fff3cd; border-radius: 5px; border-left: 3px solid #ffc107;">
                    <div style="font-weight: bold; color: #856404; margin-bottom: 5px;">
                        🔄 第${completedParagraphs + 1}段正在优化中...
                    </div>
                </div>
            `;
        }

        streamingContainer.innerHTML = html;

        // 自动滚动到最新内容
        streamingContainer.scrollTop = streamingContainer.scrollHeight;
    };



    // Close modal if clicking outside of it
    window.addEventListener('click', (event) => {
        if (event.target === modal) {
            window.closeOptimizationModal();
        }
    });
});

// --- Real-time Order Indicator ---
document.addEventListener('DOMContentLoaded', () => {
    const indicator = document.querySelector('.realtime-order-indicator');
    if (indicator) {
        const cities = ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安'];
        const services = ['内容优化', '专家润色'];

        function showRandomOrder() {
            const randomCity = cities[Math.floor(Math.random() * cities.length)];
            const randomService = services[Math.floor(Math.random() * services.length)];
            const randomTime = Math.floor(Math.random() * 5) + 1; // 1 to 5 minutes ago

            indicator.querySelector('span').textContent = `${randomCity}用户 • ${randomTime}分钟前 • ${randomService}`;

            indicator.style.display = 'flex';
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 5000); // Show for 5 seconds
        }

        // Show a random order every 15-30 seconds
        setInterval(showRandomOrder, Math.random() * 15000 + 15000);
    }

    // --- User Activity Badge ---
    const userActivityBadge = document.querySelector('.badge-top-right');
    if (userActivityBadge) {
        const userActivities = [
            { id: '1', location: '北京用户', service: '内容优化', timeAgo: '1分钟前' },
            { id: '2', location: '上海用户', service: '专家学术', timeAgo: '1分钟前' },
            { id: '3', location: '广州用户', service: '内容优化', timeAgo: '2分钟前' },
            { id: '4', location: '深圳用户', service: '专家学术', timeAgo: '2分钟前' },
            { id: '5', location: '杭州用户', service: '内容优化', timeAgo: '3分钟前' },
            { id: '6', location: '南京用户', service: '专家学术', timeAgo: '3分钟前' },
            { id: '7', location: '武汉用户', service: '内容优化', timeAgo: '4分钟前' },
            { id: '8', location: '成都用户', service: '专家学术', timeAgo: '4分钟前' },
            { id: '9', location: '西安用户', service: '内容优化', timeAgo: '5分钟前' },
            { id: '10', location: '天津用户', service: '专家学术', timeAgo: '5分钟前' },
            { id: '11', location: '重庆用户', service: '内容优化', timeAgo: '1分钟前' },
            { id: '12', location: '苏州用户', service: '专家学术', timeAgo: '1分钟前' },
            { id: '13', location: '青岛用户', service: '内容优化', timeAgo: '2分钟前' },
            { id: '14', location: '大连用户', service: '专家学术', timeAgo: '2分钟前' },
            { id: '15', location: '厦门用户', service: '内容优化', timeAgo: '3分钟前' },
            { id: '16', location: '合肥用户', service: '专家学术', timeAgo: '3分钟前' },
            { id: '17', location: '福州用户', service: '内容优化', timeAgo: '4分钟前' },
            { id: '18', location: '郑州用户', service: '专家学术', timeAgo: '4分钟前' },
            { id: '19', location: '长春用户', service: '内容优化', timeAgo: '5分钟前' },
            { id: '20', location: '石家庄用户', service: '专家学术', timeAgo: '5分钟前' },
            { id: '21', location: '南昌用户', service: '内容优化', timeAgo: '1分钟前' },
            { id: '22', location: '贵阳用户', service: '专家学术', timeAgo: '1分钟前' },
            { id: '23', location: '哈尔滨用户', service: '内容优化', timeAgo: '2分钟前' },
            { id: '24', location: '济南用户', service: '专家学术', timeAgo: '2分钟前' },
            { id: '25', location: '呼和浩特用户', service: '内容优化', timeAgo: '3分钟前' },
            { id: '26', location: '乌鲁木齐用户', service: '专家学术', timeAgo: '3分钟前' },
            { id: '27', location: '昆明用户', service: '内容优化', timeAgo: '4分钟前' },
            { id: '28', location: '兰州用户', service: '专家学术', timeAgo: '4分钟前' },
            { id: '29', location: '宁波用户', service: '内容优化', timeAgo: '5分钟前' },
            { id: '30', location: '南宁用户', service: '专家学术', timeAgo: '5分钟前' },
        ];

        let currentActivityIndex = 0;

        function updateUserActivityBadge() {
            const activity = userActivities[currentActivityIndex];
            userActivityBadge.innerHTML = `<span class="marquee-text">${activity.location} • ${activity.timeAgo} • ${activity.service}</span>`;
            currentActivityIndex = (currentActivityIndex + 1) % userActivities.length;
        }

        // Initial display
        updateUserActivityBadge();

        // Update every 2 seconds
        setInterval(updateUserActivityBadge, 6000);
    }
});

// --- 更新UI显示已上传的文档 ---
function updateUploadedDocumentUI(document) {
    debugLog('🎨 更新UI显示已上传的文档:', document, 'debug');

    const uploadArea = document.querySelector('.upload-area');
    const fileInput = document.getElementById('fileInput');

    if (uploadArea && fileInput) {
        // 更新上传区域显示
        uploadArea.innerHTML = `
            <div class="uploaded-file">
                <div class="file-icon">📄</div>
                <div class="file-info">
                    <div class="file-name">${document.name}</div>
                    <div class="file-size">已上传 • ${new Date(document.uploadTime).toLocaleString()}</div>
                </div>
                <button class="remove-file" onclick="removeUploadedFile()">×</button>
            </div>
        `;

        // 添加样式
        uploadArea.classList.add('has-file');

        // 只对非特权用户隐藏开始优化按钮
        if (!isFreeOptimizeUser()) {
            const submitBtn = document.getElementById('submitBtn');
            if (submitBtn) {
                submitBtn.style.display = 'none'; // 隐藏按钮，只通过支付后自动优化
            }
        }

        debugLog('✅ UI已更新为已上传文档状态', 'debug');
    }
}

// --- 移除已上传的文件 ---
function removeUploadedFile() {
    console.log('🗑️ 移除已上传的文件');

    // 清除全局文档变量和持久化存储
    window.currentDocument = null;
    currentDocument = null;
    localStorage.removeItem('currentDocument');
    localStorage.removeItem('pendingDocument');

    // 恢复上传区域的原始状态
    const uploadArea = document.querySelector('.upload-area');
    if (uploadArea) {
        uploadArea.innerHTML = `
            <div class="upload-icon-wrapper">
                <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7,10 12,15 17,10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
            </div>
            <h3>文档上传区域</h3>
            <p>支持 .txt, .docx 等格式，下载统一为 .docx 格式</p>
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">选择文件</button>
        `;

        uploadArea.classList.remove('has-file');
    }

    // 只对非特权用户隐藏开始优化按钮
    if (!isFreeOptimizeUser()) {
        const submitBtn = document.getElementById('submitBtn');
        if (submitBtn) {
            submitBtn.style.display = 'none'; // 保持隐藏状态
        }
    }

    console.log('✅ 已恢复到未上传状态');
}

// --- 自动触发优化功能 ---
window.autoTriggerOptimization = function() {
    console.log('🚀 开始自动触发优化');

    // 检查是否有上传的文档
    let docInfo = window.currentDocument || currentDocument || null;

    // 如果没有找到，尝试从localStorage恢复
    if (!docInfo || !docInfo.id) {
        console.log('🔍 尝试从localStorage恢复文档信息');
        const pendingDoc = localStorage.getItem('pendingDocument');
        if (pendingDoc) {
            try {
                docInfo = JSON.parse(pendingDoc);
                // 设置全局变量
                window.currentDocument = docInfo;
                currentDocument = docInfo;
                console.log('✅ 从localStorage恢复文档:', docInfo.name);
            } catch (e) {
                console.error('❌ 解析localStorage文档失败:', e);
            }
        }
    }

    if (!docInfo || !docInfo.id) {
        console.log('❌ 没有找到上传的文档，无法自动优化');
        showNotification('没有找到上传的文档，请重新上传', 'error');
        return;
    }

    console.log('📄 找到文档:', docInfo.name);

    // 确保在粘贴文本标签页（因为文档内容已经显示在这里）
    const uploadTab = document.querySelector('[data-tab="upload"]');
    const textTab = document.querySelector('[data-tab="text"]');
    const uploadTabContent = document.getElementById('uploadTabContent');
    const textTabContent = document.getElementById('textTabContent');

    if (uploadTab && textTab && uploadTabContent && textTabContent) {
        // 确保在粘贴文本标签页
        uploadTab.classList.remove('active');
        textTab.classList.add('active');
        uploadTabContent.classList.remove('active');
        textTabContent.classList.add('active');
        currentTab = 'text';
        console.log('📋 确保在粘贴文本标签页');

        // 确保文档内容在文本框中
        const textarea = document.getElementById('content');
        if (textarea && docInfo.content) {
            textarea.value = docInfo.content;
            console.log('📝 确保文档内容在文本框中');
        }
    }

    // 显示优化弹窗
    const modal = document.getElementById('optimizationModal');
    if (modal && window.openOptimizationModal) {
        console.log('🎯 显示优化弹窗');
        window.openOptimizationModal();
    }

    // 直接调用优化逻辑，而不是点击按钮
    setTimeout(() => {
        console.log('🎯 开始执行自动优化');

        // 检查文档内容
        if (!docInfo || !docInfo.content) {
            console.error('❌ 没有文档内容可以优化');
            console.error('docInfo:', docInfo);

            // 安全调用showNotification
            if (typeof showNotification === 'function') {
                showNotification('没有文档内容可以优化', 'error');
            } else {
                alert('没有文档内容可以优化');
            }
            return;
        }

        // 直接调用优化API
        const optimizeType = 'academic'; // 默认优化类型
        const text = docInfo.content;

        // 调用优化函数
        startOptimization(text, optimizeType, docInfo.name);

    }, 1000);
};

// ==================== 学术专家服务功能 ====================

// 显示学术专家服务登录提示弹窗
function showExpertServiceLoginModal() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    `;

    modal.innerHTML = `
        <div style="
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        ">
            <div style="margin-bottom: 20px;">
                <div style="
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, #8A2BE2, #9370DB);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 15px;
                    font-size: 24px;
                ">🎓</div>
                <h3 style="color: #333; margin-bottom: 10px;">学术专家服务</h3>
                <p style="color: #666; line-height: 1.6;">
                    为了确保您能及时接收处理结果和查看历史记录，<br>
                    学术专家服务需要注册账户。
                </p>
            </div>

            <div style="
                background: #f8f9ff;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 25px;
                text-align: left;
            ">
                <h4 style="color: #8A2BE2; margin-bottom: 15px;">✅ 注册账户的优势</h4>
                <ul style="color: #666; line-height: 1.8; margin: 0; padding-left: 20px;">
                    <li>🔒 安全保护您的学术文档</li>
                    <li>📧 及时通知处理进度</li>
                    <li>📋 随时查看历史记录</li>
                    <li>📥 方便下载处理结果</li>
                </ul>
            </div>

            <div style="display: flex; gap: 15px; justify-content: center;">
                <button onclick="closeExpertLoginModal()" style="
                    padding: 12px 24px;
                    border: 2px solid #ddd;
                    background: white;
                    color: #666;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                ">取消</button>
                <button onclick="goToLogin()" style="
                    padding: 12px 24px;
                    border: none;
                    background: linear-gradient(135deg, #8A2BE2, #9370DB);
                    color: white;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                ">立即登录/注册</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 点击背景关闭
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeExpertLoginModal();
        }
    });
}

// 关闭专家服务登录提示弹窗
function closeExpertLoginModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

// 跳转到登录页面
function goToLogin() {
    closeExpertLoginModal();
    window.location.href = 'login.html';
}

// 显示学术专家服务客服联系弹窗
async function showExpertServiceContactModal() {
    try {
        // 定义API基础URL
        const API_BASE_URL = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
            ? window.location.protocol + '//' + window.location.hostname + ':3001/api'
            : '/api';

        // 获取认证令牌
        const token = localStorage.getItem('token');
        const headers = {
            'Content-Type': 'application/json'
        };

        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        // 获取客服二维码
        const response = await fetch(`${API_BASE_URL}/settings/customer-service-qr`, {
            method: 'GET',
            headers: headers
        });
        let contactInfo = {
            qrCodeUrl: '',
            contactEmail: '客服邮箱：<EMAIL>',
            workingHours: '工作时间：周一至周五 9:00-18:00',
            wechat: '微信客服：WriterPro_Service'
        };

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                contactInfo = {
                    qrCodeUrl: data.qrCodeUrl || '',
                    contactEmail: data.contactEmail || contactInfo.contactEmail,
                    workingHours: data.workingHours || contactInfo.workingHours,
                    wechat: data.wechat || contactInfo.wechat
                };
            }
        }

        // 创建弹窗HTML
        const modalHtml = `
            <div id="expertContactModal" class="modal" style="display: block; z-index: 10000;">
                <div class="modal-content" style="max-width: 500px; margin: 10% auto; padding: 30px; text-align: center; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); background: white;">
                    <div class="modal-header" style="border-bottom: none; padding-bottom: 20px; position: relative;">
                        <h2 style="color: #8A2BE2; margin: 0; font-size: 24px;">
                            🎓 学术专家服务
                        </h2>
                        <button type="button" class="close" onclick="closeExpertContactModal()" style="position: absolute; top: -10px; right: -10px; background: none; border: none; font-size: 28px; cursor: pointer; color: #999;">
                            ×
                        </button>
                    </div>
                    <div class="modal-body" style="padding: 20px 0;">
                        <div style="background: #f8f9ff; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                            <h3 style="color: #333; margin-bottom: 15px;">✅ 支付成功！</h3>
                            <p style="color: #666; line-height: 1.6; margin-bottom: 0;">
                                感谢您选择我们的学术专家服务！<br>
                                我们的专业团队将为您提供高质量的学术润色服务。
                            </p>
                        </div>

                        <div style="margin-bottom: 25px;">
                            <h4 style="color: #8A2BE2; margin-bottom: 15px;">📞 联系我们的专家团队</h4>

                            <div style="text-align: left; background: #fff; padding: 20px; border-radius: 8px; border: 1px solid #e0e0e0; margin-bottom: 20px;">
                                <div style="margin-bottom: 12px;">
                                    <strong style="color: #333;">📱 ${contactInfo.contactPhone}</strong>
                                </div>
                                <div style="margin-bottom: 12px;">
                                    <strong style="color: #333;">📧 ${contactInfo.contactEmail}</strong>
                                </div>
                                <div style="margin-bottom: 12px;">
                                    <strong style="color: #333;">💬 ${contactInfo.wechat}</strong>
                                </div>
                                <div style="margin-bottom: 0;">
                                    <strong style="color: #333;">🕒 ${contactInfo.workingHours}</strong>
                                </div>
                            </div>

                            ${contactInfo.qrCodeUrl ? `
                                <div style="margin-bottom: 15px;">
                                    <img src="${contactInfo.qrCodeUrl}" alt="客服微信二维码" style="width: 150px; height: 150px; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                                </div>
                                <p style="color: #666; font-size: 14px; margin-bottom: 20px;">
                                    扫描上方二维码添加客服微信<br>
                                    告知您的具体学术需求和要求
                                </p>
                            ` : `
                                <p style="color: #666; margin-bottom: 20px;">
                                    请联系我们的客服团队<br>
                                    告知您的具体学术需求和要求
                                </p>
                            `}
                        </div>

                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #ffc107;">
                            <p style="color: #856404; margin: 0; font-size: 14px; text-align: left;">
                                <strong>📋 服务流程：</strong><br>
                                1. 添加客服微信，说明您的学术需求<br>
                                2. 专家团队评估并开始处理<br>
                                3. 完成后文件将上传到您的历史订单<br>
                                4. 您可以在历史记录中下载结果文件
                            </p>
                        </div>

                        <div style="margin-top: 25px;">
                            <button onclick="window.location.href='history.html'" class="btn" style="background: #8A2BE2; color: white; padding: 12px 30px; border: none; border-radius: 25px; font-size: 16px; cursor: pointer; margin-right: 10px; transition: all 0.3s;">
                                📋 查看历史订单
                            </button>
                            <button onclick="closeExpertContactModal()" class="btn" style="background: #6c757d; color: white; padding: 12px 30px; border: none; border-radius: 25px; font-size: 16px; cursor: pointer; transition: all 0.3s;">
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            #expertContactModal .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            }
            #expertContactModal .modal {
                background: rgba(0,0,0,0.5);
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
            #expertContactModal .close:hover {
                color: #333;
            }
        `;
        document.head.appendChild(style);

    } catch (error) {
        console.error('显示专家服务联系弹窗失败:', error);

        // 手动显示通知，避免作用域问题
        const notification = document.createElement('div');
        notification.classList.add('notification', 'success');
        notification.innerHTML = `
            <span class="notification-icon">✅</span>
            <span>支付成功！请联系客服获取专家服务。</span>
        `;

        const notificationContainer = document.getElementById('notificationContainer') || document.body;
        notificationContainer.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// 关闭学术专家联系弹窗
window.closeExpertContactModal = function() {
    const modal = document.getElementById('expertContactModal');
    if (modal) {
        modal.remove();
    }
}

// 获取当前内容（用于订单）
function getCurrentContent() {
    const textInput = document.getElementById('textInput');
    const fileContent = document.getElementById('fileContent');

    if (textInput && textInput.value.trim()) {
        return {
            type: 'text',
            content: textInput.value.trim(),
            wordCount: totalWordCount
        };
    } else if (fileContent && fileContent.textContent.trim()) {
        return {
            type: 'file',
            content: fileContent.textContent.trim(),
            wordCount: totalWordCount,
            fileName: document.getElementById('fileName')?.textContent || '未知文件'
        };
    }

    return {
        type: 'unknown',
        content: '',
        wordCount: 0
    };
}

// 暴露全局函数
window.showExpertServiceContactModal = showExpertServiceContactModal;
window.showExpertServiceLoginModal = showExpertServiceLoginModal;
window.closeExpertLoginModal = closeExpertLoginModal;
window.goToLogin = goToLogin;

// 移动端导航菜单控制
class MobileNavController {
    constructor() {
        this.menuToggle = document.getElementById('mobileMenuToggle');
        this.navbarNav = document.getElementById('navbarNav');
        this.isOpen = false;

        this.init();
    }

    init() {
        if (!this.menuToggle || !this.navbarNav) return;

        // 绑定点击事件
        this.menuToggle.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggle();
        });

        // 点击导航链接时关闭菜单（移动端）
        this.navbarNav.addEventListener('click', (e) => {
            if (e.target.matches('a') && window.innerWidth <= 768) {
                this.close();
            }
        });

        // 点击外部区域关闭菜单
        document.addEventListener('click', (e) => {
            if (this.isOpen &&
                !this.navbarNav.contains(e.target) &&
                !this.menuToggle.contains(e.target)) {
                this.close();
            }
        });

        // 窗口大小变化时处理
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768 && this.isOpen) {
                this.close();
            }
        });

        // ESC键关闭菜单
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        this.navbarNav.classList.add('mobile-show');
        this.isOpen = true;

        // 更新按钮图标为关闭图标
        this.menuToggle.innerHTML = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        `;

        this.menuToggle.setAttribute('aria-expanded', 'true');
    }

    close() {
        this.navbarNav.classList.remove('mobile-show');
        this.isOpen = false;

        // 恢复汉堡菜单图标
        this.menuToggle.innerHTML = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        `;

        this.menuToggle.setAttribute('aria-expanded', 'false');
    }
}

// 初始化移动端导航控制器
document.addEventListener('DOMContentLoaded', () => {
    window.mobileNavController = new MobileNavController();
});

// 显示专家服务全屏联系弹窗
async function showExpertServiceFullscreenModal() {
    try {
        console.log('显示专家服务全屏联系弹窗');

        // 定义API基础URL
        const API_BASE_URL = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
            ? window.location.protocol + '//' + window.location.hostname + ':3001/api'
            : '/api';

        // 获取联系信息
        let contactInfo = {
            qrCodeUrl: '',
            contactEmail: '客服邮箱：<EMAIL>',
            workingHours: '工作时间：周一至周五 9:00-18:00',
            wechat: '微信客服：WriterPro_Service'
        };

        try {
            const response = await fetch(`${API_BASE_URL}/settings/customer-service-qr`);
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    contactInfo = {
                        qrCodeUrl: data.qrCodeUrl || '',
                        contactEmail: data.contactEmail || contactInfo.contactEmail,
                        workingHours: data.workingHours || contactInfo.workingHours,
                        wechat: data.wechat || contactInfo.wechat
                    };
                }
            }
        } catch (error) {
            console.error('获取联系信息失败，使用默认信息:', error);
        }

        // 创建全屏弹窗HTML
        const modalHTML = `
            <div id="expertFullscreenModal" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease-out;
            ">
                <div style="
                    background: white;
                    border-radius: 20px;
                    max-width: 600px;
                    width: 90%;
                    max-height: 90vh;
                    overflow-y: auto;
                    padding: 40px;
                    position: relative;
                    animation: slideUp 0.4s ease-out;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                ">
                    <!-- 关闭按钮 -->
                    <button onclick="closeExpertFullscreenModal()" style="
                        position: absolute;
                        top: 15px;
                        right: 15px;
                        background: none;
                        border: none;
                        font-size: 24px;
                        cursor: pointer;
                        color: #666;
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='none'">
                        ✕
                    </button>

                    <!-- 头部 -->
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="
                            color: #8A2BE2;
                            font-size: 28px;
                            margin-bottom: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 10px;
                        ">
                            🎓 学术专家服务
                        </h1>
                        <p style="color: #666; font-size: 16px; line-height: 1.6;">
                            专业的学术润色团队为您提供高质量服务
                        </p>
                    </div>

                    <!-- 支付成功提示 -->
                    <div style="
                        background: #f8f9ff;
                        border: 2px solid #8A2BE2;
                        border-radius: 15px;
                        padding: 20px;
                        margin-bottom: 30px;
                        text-align: center;
                    ">
                        <h2 style="color: #28a745; font-size: 24px; margin-bottom: 10px;">
                            ✅ 支付成功！
                        </h2>
                        <p style="color: #333; font-size: 16px; line-height: 1.6;">
                            感谢您选择我们的学术专家服务！我们的专业团队将为您提供高质量的学术润色服务。
                        </p>
                    </div>

                    <!-- 联系信息 -->
                    <div style="
                        background: #f8f9fa;
                        border-radius: 15px;
                        padding: 30px;
                        margin-bottom: 30px;
                    ">
                        <h3 style="
                            color: #8A2BE2;
                            font-size: 20px;
                            margin-bottom: 20px;
                            text-align: center;
                        ">
                            📞 联系我们的专家团队
                        </h3>

                        <div style="display: flex; flex-direction: column; gap: 15px;">
                            <div style="
                                display: flex;
                                align-items: center;
                                padding: 12px;
                                background: white;
                                border-radius: 10px;
                                box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                            ">
                                <div style="font-size: 20px; margin-right: 15px; width: 30px; text-align: center;">📧</div>
                                <div style="font-size: 16px; color: #333; font-weight: 500;">${contactInfo.contactEmail}</div>
                            </div>

                            <div style="
                                display: flex;
                                align-items: center;
                                padding: 12px;
                                background: white;
                                border-radius: 10px;
                                box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                            ">
                                <div style="font-size: 20px; margin-right: 15px; width: 30px; text-align: center;">💬</div>
                                <div style="font-size: 16px; color: #333; font-weight: 500;">${contactInfo.wechat}</div>
                            </div>

                            <div style="
                                display: flex;
                                align-items: center;
                                padding: 12px;
                                background: white;
                                border-radius: 10px;
                                box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                            ">
                                <div style="font-size: 20px; margin-right: 15px; width: 30px; text-align: center;">🕒</div>
                                <div style="font-size: 16px; color: #333; font-weight: 500;">${contactInfo.workingHours}</div>
                            </div>
                        </div>

                        ${contactInfo.qrCodeUrl ? `
                            <div style="text-align: center; margin-top: 20px;">
                                <img src="${contactInfo.qrCodeUrl}" alt="客服微信二维码" style="
                                    width: 200px;
                                    height: 200px;
                                    border-radius: 15px;
                                    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
                                ">
                                <p style="margin-top: 15px; color: #666; font-size: 14px;">
                                    扫描上方二维码添加客服微信<br>告知您的具体学术需求和要求
                                </p>
                            </div>
                        ` : ''}
                    </div>

                    <!-- 服务流程 -->
                    <div style="
                        background: #fff3cd;
                        border-left: 4px solid #ffc107;
                        border-radius: 10px;
                        padding: 20px;
                        margin-bottom: 30px;
                    ">
                        <h4 style="color: #856404; font-size: 18px; margin-bottom: 20px; text-align: center;">
                            📋 服务流程
                        </h4>

                        <!-- 流程步骤 - 极致大气设计 -->
                        <div style="
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            gap: 0;
                            max-width: 500px;
                            margin: 0 auto;
                        ">
                            <!-- 步骤1 -->
                            <div style="
                                display: flex;
                                align-items: center;
                                gap: 20px;
                                width: 100%;
                                padding: 15px 20px;
                                background: linear-gradient(135deg, #8A2BE2 0%, #9B59B6 100%);
                                border-radius: 15px;
                                box-shadow: 0 8px 25px rgba(138, 43, 226, 0.3);
                                margin-bottom: 8px;
                                transition: all 0.3s ease;
                            ">
                                <div style="
                                    background: rgba(255, 255, 255, 0.2);
                                    color: white;
                                    width: 40px;
                                    height: 40px;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-weight: bold;
                                    font-size: 18px;
                                    flex-shrink: 0;
                                    border: 2px solid rgba(255, 255, 255, 0.3);
                                ">①</div>
                                <div style="
                                    color: white;
                                    font-size: 16px;
                                    font-weight: 600;
                                    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                                ">
                                    联系客服，说明您的学术需求和具体要求
                                </div>
                            </div>

                            <!-- 箭头 -->
                            <div style="
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                width: 60px;
                                height: 30px;
                                margin: 5px 0;
                            ">
                                <div style="
                                    color: #8A2BE2;
                                    font-size: 28px;
                                    font-weight: bold;
                                    text-shadow: 0 2px 4px rgba(138, 43, 226, 0.3);
                                    animation: bounce 2s infinite;
                                ">↓</div>
                            </div>

                            <!-- 步骤2 -->
                            <div style="
                                display: flex;
                                align-items: center;
                                gap: 20px;
                                width: 100%;
                                padding: 15px 20px;
                                background: linear-gradient(135deg, #8A2BE2 0%, #9B59B6 100%);
                                border-radius: 15px;
                                box-shadow: 0 8px 25px rgba(138, 43, 226, 0.3);
                                margin-bottom: 8px;
                                transition: all 0.3s ease;
                            ">
                                <div style="
                                    background: rgba(255, 255, 255, 0.2);
                                    color: white;
                                    width: 40px;
                                    height: 40px;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-weight: bold;
                                    font-size: 18px;
                                    flex-shrink: 0;
                                    border: 2px solid rgba(255, 255, 255, 0.3);
                                ">②</div>
                                <div style="
                                    color: white;
                                    font-size: 16px;
                                    font-weight: 600;
                                    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                                ">
                                    专家团队评估文档并制定润色方案
                                </div>
                            </div>

                            <!-- 箭头 -->
                            <div style="
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                width: 60px;
                                height: 30px;
                                margin: 5px 0;
                            ">
                                <div style="
                                    color: #8A2BE2;
                                    font-size: 28px;
                                    font-weight: bold;
                                    text-shadow: 0 2px 4px rgba(138, 43, 226, 0.3);
                                    animation: bounce 2s infinite;
                                ">↓</div>
                            </div>

                            <!-- 步骤3 -->
                            <div style="
                                display: flex;
                                align-items: center;
                                gap: 20px;
                                width: 100%;
                                padding: 15px 20px;
                                background: linear-gradient(135deg, #8A2BE2 0%, #9B59B6 100%);
                                border-radius: 15px;
                                box-shadow: 0 8px 25px rgba(138, 43, 226, 0.3);
                                margin-bottom: 8px;
                                transition: all 0.3s ease;
                            ">
                                <div style="
                                    background: rgba(255, 255, 255, 0.2);
                                    color: white;
                                    width: 40px;
                                    height: 40px;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-weight: bold;
                                    font-size: 18px;
                                    flex-shrink: 0;
                                    border: 2px solid rgba(255, 255, 255, 0.3);
                                ">③</div>
                                <div style="
                                    color: white;
                                    font-size: 16px;
                                    font-weight: 600;
                                    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                                ">
                                    专业学术编辑进行深度润色和优化
                                </div>
                            </div>

                            <!-- 箭头 -->
                            <div style="
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                width: 60px;
                                height: 30px;
                                margin: 5px 0;
                            ">
                                <div style="
                                    color: #8A2BE2;
                                    font-size: 28px;
                                    font-weight: bold;
                                    text-shadow: 0 2px 4px rgba(138, 43, 226, 0.3);
                                    animation: bounce 2s infinite;
                                ">↓</div>
                            </div>

                            <!-- 步骤4 -->
                            <div style="
                                display: flex;
                                align-items: center;
                                gap: 20px;
                                width: 100%;
                                padding: 15px 20px;
                                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                                border-radius: 15px;
                                box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
                                margin-bottom: 8px;
                                transition: all 0.3s ease;
                            ">
                                <div style="
                                    background: rgba(255, 255, 255, 0.2);
                                    color: white;
                                    width: 40px;
                                    height: 40px;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-weight: bold;
                                    font-size: 20px;
                                    flex-shrink: 0;
                                    border: 2px solid rgba(255, 255, 255, 0.3);
                                ">✓</div>
                                <div style="
                                    color: white;
                                    font-size: 16px;
                                    font-weight: 600;
                                    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                                ">
                                    质量检查和最终交付优化后的文档
                                </div>
                            </div>

                            <!-- 箭头 -->
                            <div style="
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                width: 60px;
                                height: 30px;
                                margin: 5px 0;
                            ">
                                <div style="
                                    color: #28a745;
                                    font-size: 28px;
                                    font-weight: bold;
                                    text-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
                                    animation: bounce 2s infinite;
                                ">↓</div>
                            </div>

                            <!-- 步骤5 - 历史中心 -->
                            <div style="
                                display: flex;
                                align-items: center;
                                gap: 20px;
                                width: 100%;
                                padding: 15px 20px;
                                background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
                                border-radius: 15px;
                                box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
                                transition: all 0.3s ease;
                                cursor: pointer;
                            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 12px 35px rgba(23, 162, 184, 0.4)'"
                               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 25px rgba(23, 162, 184, 0.3)'">
                                <div style="
                                    background: rgba(255, 255, 255, 0.2);
                                    color: white;
                                    width: 40px;
                                    height: 40px;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-weight: bold;
                                    font-size: 18px;
                                    flex-shrink: 0;
                                    border: 2px solid rgba(255, 255, 255, 0.3);
                                ">📋</div>
                                <div style="
                                    color: white;
                                    font-size: 16px;
                                    font-weight: 600;
                                    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                                ">
                                    点击历史中心查看
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="
                        display: flex;
                        gap: 15px;
                        justify-content: center;
                        flex-wrap: wrap;
                    ">
                        <button onclick="window.location.href='history.html'" style="
                            padding: 12px 24px;
                            border: none;
                            border-radius: 10px;
                            font-size: 16px;
                            font-weight: 500;
                            cursor: pointer;
                            background: #8A2BE2;
                            color: white;
                            display: inline-flex;
                            align-items: center;
                            gap: 8px;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='#7B1FA2'; this.style.transform='translateY(-2px)'"
                           onmouseout="this.style.background='#8A2BE2'; this.style.transform='translateY(0)'">
                            📋 查看历史记录
                        </button>

                        <button onclick="closeExpertFullscreenModal()" style="
                            padding: 12px 24px;
                            border: none;
                            border-radius: 10px;
                            font-size: 16px;
                            font-weight: 500;
                            cursor: pointer;
                            background: #6c757d;
                            color: white;
                            display: inline-flex;
                            align-items: center;
                            gap: 8px;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='#5a6268'; this.style.transform='translateY(-2px)'"
                           onmouseout="this.style.background='#6c757d'; this.style.transform='translateY(0)'">
                            ✕ 关闭
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes slideUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% {
                    transform: translateY(0);
                }
                40% {
                    transform: translateY(-8px);
                }
                60% {
                    transform: translateY(-4px);
                }
            }
            @media (max-width: 768px) {
                #expertFullscreenModal > div {
                    width: 95% !important;
                    padding: 30px 20px !important;
                    margin: 10px !important;
                }
                /* 移动端流程步骤优化 */
                #expertFullscreenModal .process-step {
                    padding: 12px 15px !important;
                    gap: 15px !important;
                }
                #expertFullscreenModal .process-step-number {
                    width: 35px !important;
                    height: 35px !important;
                    font-size: 16px !important;
                }
                #expertFullscreenModal .process-step-text {
                    font-size: 14px !important;
                }
            }
        `;
        document.head.appendChild(style);

        // 添加弹窗到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // 阻止背景滚动
        document.body.style.overflow = 'hidden';

    } catch (error) {
        console.error('显示专家服务全屏弹窗失败:', error);

        // 显示简单的成功通知作为备选
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            z-index: 10001;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        `;
        notification.textContent = '✅ 支付成功！请联系客服获取专家服务。';
        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// 关闭专家服务全屏弹窗
function closeExpertFullscreenModal() {
    const modal = document.getElementById('expertFullscreenModal');
    if (modal) {
        modal.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => {
            modal.remove();
            document.body.style.overflow = '';
        }, 300);
    }
}

// 复制专家联系信息
function copyExpertContactInfo() {
    const contactInfo = `客服邮箱：<EMAIL>
微信客服：WriterPro_Service
工作时间：周一至周五 9:00-18:00`;

    navigator.clipboard.writeText(contactInfo).then(() => {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '✅ 已复制';
        btn.style.background = '#28a745';

        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = '#8A2BE2';
        }, 2000);
    }).catch(err => {
        console.error('复制失败:', err);
        alert('复制失败，请手动复制联系信息');
    });
}

// 导出函数供全局使用
window.showExpertServiceFullscreenModal = showExpertServiceFullscreenModal;
window.closeExpertFullscreenModal = closeExpertFullscreenModal;
window.copyExpertContactInfo = copyExpertContactInfo;

// TempUserManager - 临时用户管理
window.TempUserManager = {
    // 生成或获取临时用户ID
    getTempUserId() {
        let tempId = localStorage.getItem('temp_user_id');
        if (!tempId) {
            tempId = 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('temp_user_id', tempId);
            console.log('生成新的临时用户ID:', tempId);
        }
        return tempId;
    },

    // 保存临时文档到本地存储
    saveTempDocument(docInfo) {
        const tempId = this.getTempUserId();
        let history = JSON.parse(localStorage.getItem('temp_documents') || '[]');

        const tempDoc = {
            ...docInfo,
            tempUserId: tempId,
            timestamp: Date.now(),
            id: 'temp_doc_' + Date.now()
        };

        history.push(tempDoc);
        localStorage.setItem('temp_documents', JSON.stringify(history));
        console.log('保存临时文档:', tempDoc);

        return tempDoc;
    },

    // 获取本地临时文档历史
    getTempDocuments() {
        const tempId = this.getTempUserId();
        const history = JSON.parse(localStorage.getItem('temp_documents') || '[]');

        // 过滤出当前临时用户的文档，并按时间倒序排列
        return history
            .filter(doc => doc.tempUserId === tempId)
            .sort((a, b) => b.timestamp - a.timestamp);
    },

    // 清理过期的临时文档（30天）
    cleanExpiredDocuments() {
        const history = JSON.parse(localStorage.getItem('temp_documents') || '[]');
        const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);

        const validDocs = history.filter(doc => doc.timestamp > thirtyDaysAgo);
        localStorage.setItem('temp_documents', JSON.stringify(validDocs));

        console.log(`清理过期文档，保留 ${validDocs.length} 个有效文档`);
    },

    // 检查是否有临时文档
    hasTempDocuments() {
        return this.getTempDocuments().length > 0;
    }
};