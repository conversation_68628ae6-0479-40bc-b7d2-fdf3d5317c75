<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WriterPro - AI文本优化工具</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="apple-style.css">
    <link rel="stylesheet" href="minimal-footer.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        #aiServiceCard, .expert-service-card {
            display: flex;
            flex-direction: column;
            justify-content: space-between; /* Ensure content pushes to top and bottom */
        }

        #aiServiceCard .card-body, 
        .expert-service-card .service-features {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .price-display {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            padding: 16px 24px; /* 调整内边距 */
            box-sizing: border-box;
            margin-top: auto; /* Push to bottom */
        }

        .price-summary-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            background: linear-gradient(135deg, #FF7E5F 0%, #FF6B35 100%); /* 强烈的橙色渐变 */
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4); /* 橙色辉光效果 */
            font-size: 1.1rem;
            font-weight: 600;
            color: white; /* 白色文字以获得最大对比度 */
            margin-top: 16px;
            padding: 16px;
            border-radius: 12px;
            box-sizing: border-box;
            transition: all 0.3s ease; /* 添加过渡效果 */
        }

        .price-summary-box .pay-now-btn-card {
            background: linear-gradient(135deg, #0A1931 0%, #1F4287 100%); /* 深海权威渐变 */
            color: #FFFFFF;
            font-weight: 800;
            border-radius: 8px;
            border: none;
            padding: 12px 26px;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(31, 66, 135, 0.4); /* 蓝色辉光 */
            backdrop-filter: none;
            -webkit-backdrop-filter: none;
        }

        .price-summary-box .pay-now-btn-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(31, 66, 135, 0.6); /* 增强辉光 */
            background: linear-gradient(135deg, #1F4287 0%, #0A1931 100%); /* 悬停时渐变反转 */
        }

        .price-summary-box span span {
             font-weight: 800; /* 更粗的字重 */
             color: #FFFFFF; /* 纯白，确保高对比度 */
             text-shadow: 0 1px 3px rgba(0,0,0,0.1); /* 轻微文字阴影增加立体感 */
        }

        /* Payment Modal Styles */
        .modal-content.payment-modal-content {
            background: rgba(255, 255, 255, 0.8); /* 半透明白色毛玻璃效果 */
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 24px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1) inset; /* 多层阴影和边框光效 */
            min-height: 280px;
            padding: 32px;
            display: flex;
            flex-direction: column;
            gap: 24px; /* 各区域间距 */
            transition: all 0.3s ease;
        }

        .modal-content.payment-modal-content:hover {
            transform: translateY(-6px); /* 悬停：整个卡片向上浮动6px */
        }

        /* Top Title Area */
        .payment-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px; /* 标题区域间距 */
        }

        .payment-card-header .payment-title-group {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .payment-card-header .payment-icon-wrapper {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #FF9900 0%, #FF6B35 100%); /* 橙色渐变背景 */
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
        }

        .payment-card-header .payment-icon-wrapper svg {
            width: 24px;
            height: 24px;
            color: white;
        }

        .payment-card-header .payment-title {
            font-size: 18px;
            font-weight: 700;
            color: #1C1C1E; /* 深灰色 */
        }

        .payment-card-header .payment-subtitle {
            font-size: 14px;
            font-weight: 500;
            color: #636366; /* 浅灰色 */
        }

        .payment-card-header .realtime-order-indicator {
            background-color: rgba(255, 59, 48, 0.1);
            border: 1px solid rgba(255, 59, 48, 0.3);
            border-radius: 16px;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #1C1C1E;
        }

        .payment-card-header .realtime-order-indicator .pulse-dot {
            width: 8px;
            height: 8px;
            background-color: #FF3B30;
            border-radius: 50%;
            animation: pulse 2s infinite cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes pulse {
            0% { transform: scale(0.8); opacity: 0.7; }
            50% { transform: scale(1.2); opacity: 1; }
            100% { transform: scale(0.8); opacity: 0.7; }
        }

        /* QR Code Display Area */
        .qr-code-display-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 24px; /* 二维码区域上下间距 */
        }

        .qr-code-display-area .qr-code-placeholder {
            background: linear-gradient(135deg, rgba(242, 242, 247, 0.8) 0%, rgba(255, 255, 255, 0.8) 100%); /* 浅灰色渐变 */
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 12px;
            padding: 24px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            width: 100%;
            max-width: 250px;
            box-sizing: border-box;
        }

        .qr-code-display-area .qr-code-placeholder svg {
            width: 48px;
            height: 48px;
            color: #AEAEB2; /* 浅灰色 */
            margin-bottom: 12px;
        }

        .qr-code-display-area .qr-code-placeholder p {
            font-size: 15px;
            color: #636366;
            margin: 0;
        }

        .qr-code-display-area .qr-code-container {
            background-color: white;
            padding: 12px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .qr-code-display-area .qr-code-container img {
            width: 160px;
            height: 160px;
            display: block;
        }

        .qr-code-display-area .qr-code-logo-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 32px;
            height: 32px;
            background-color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qr-code-display-area .qr-code-info {
            text-align: center;
            margin-top: 12px;
        }

        .qr-code-display-area .qr-code-info .qr-code-title {
            font-size: 18px;
            font-weight: 700;
            color: #1C1C1E;
            margin: 0 0 4px 0;
        }

        .qr-code-display-area .qr-code-info .qr-code-service {
            font-size: 14px;
            color: #636366;
            margin: 0;
        }

        .qr-code-display-area .security-tips {
            display: flex;
            flex-direction: column;
            gap: 2px;
            margin-top: 12px;
        }

        .qr-code-display-area .security-tips .tip-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #636366;
        }

        .qr-code-display-area .security-tips .tip-item svg {
            width: 14px;
            height: 14px;
        }

        .qr-code-display-area .security-tips .tip-item.star svg {
            color: #FFC107; /* Gold color for star */
        }

        .qr-code-display-area .security-tips .tip-item.check svg {
            color: #28a745; /* Green color for check */
        }

        .qr-code-display-area .countdown-timer {
            background-color: rgba(240, 240, 240, 0.5);
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 12px;
            color: #636366;
            margin-top: 12px;
        }

        .qr-code-display-area .countdown-timer span {
            font-weight: 600;
            color: #FF6B35; /* Orange for countdown numbers */
        }

        /* Payment Methods */
        .payment-methods {
            display: flex;
            gap: 12px; /* 按钮间距 */
            margin-bottom: 24px;
        }

        .payment-methods .payment-method-btn {
            flex: 1;
            height: 40px;
            background-color: white;
            border: 1px solid #E0E0E0; /* 浅灰边框 */
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 15px;
            font-weight: 600;
            color: #1C1C1E;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .payment-methods .payment-method-btn svg {
            width: 20px;
            height: 20px;
        }

        /* WeChat Button */
        .payment-methods .payment-method-btn.wechat:hover {
            border-color: #28a745;
            background-color: rgba(40, 167, 69, 0.05);
            transform: translateY(-2px);
        }

        .payment-methods .payment-method-btn.wechat.active {
            border: 2px solid #07C160; /* 绿色边框 */
            background-color: rgba(7, 193, 96, 0.1); /* 浅绿色背景 */
            color: #07C160;
        }

        /* Alipay Button */
        .payment-methods .payment-method-btn.alipay:hover {
            border-color: #007AFF;
            background-color: rgba(0, 122, 255, 0.05);
            transform: translateY(-2px);
        }

        .payment-methods .payment-method-btn.alipay.active {
            border: 2px solid #00A0E9; /* 蓝色边框 */
            background-color: rgba(0, 160, 233, 0.1); /* 浅蓝色背景 */
            color: #00A0E9;
        }

        /* Fuiou Alipay Button */
        .payment-methods .payment-method-btn.fuiou-alipay:hover {
            border-color: #1677FF;
            background-color: rgba(22, 119, 255, 0.05);
            transform: translateY(-2px);
        }

        .payment-methods .payment-method-btn.fuiou-alipay.active {
            border: 2px solid #1677FF; /* 支付宝蓝色边框 */
            background-color: rgba(22, 119, 255, 0.1); /* 浅蓝色背景 */
            color: #1677FF;
        }

        /* Fuiou Wechat Button */
        .payment-methods .payment-method-btn.fuiou-wechat:hover {
            border-color: #07C160;
            background-color: rgba(7, 193, 96, 0.05);
            transform: translateY(-2px);
        }

        .payment-methods .payment-method-btn.fuiou-wechat.active {
            border: 2px solid #07C160; /* 微信绿色边框 */
            background-color: rgba(7, 193, 96, 0.1); /* 浅绿色背景 */
            color: #07C160;
        }

        /* 维护状态样式 */
        .payment-methods .payment-method-btn.maintenance {
            opacity: 0.6;
            cursor: not-allowed;
            background-color: #f5f5f5;
            border-color: #d0d0d0;
            color: #999;
        }

        .payment-methods .payment-method-btn.maintenance:hover {
            transform: none;
            border-color: #d0d0d0;
            background-color: #f5f5f5;
        }

        .payment-methods .payment-method-btn.maintenance svg {
            opacity: 0.5;
        }

        /* Bottom Payment Area */
        .final-price-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            margin-bottom: 8px;
        }

        .final-price-display p {
            font-size: 20px;
            font-weight: 700;
            color: #1C1C1E; /* 深色文字 */
            margin: 0;
        }

        .final-price-display .primary-btn.pay-now-btn {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%); /* 橙色渐变背景 */
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .final-price-display .primary-btn.pay-now-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 24px rgba(255, 107, 53, 0.3); /* 阴影加深 */
        }

        /* Security Assurance Area */
        .security-assurance {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 12px;
            color: #636366; /* 浅灰色 */
            flex-wrap: wrap;
            text-align: center;
        }

        .security-assurance .icon-check-circle {
            width: 18px;
            height: 18px;
            background-color: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .security-assurance .icon-check-circle svg {
            width: 12px;
            height: 12px;
            color: white;
        }

        .security-assurance .separator {
            color: #AEAEB2;
        }

        /* Responsive adjustments for payment modal */
        @media (max-width: 768px) {
            .modal-content.payment-modal-content {
                padding: 24px;
                border-radius: 16px;
            }
            .qr-code-display-area .qr-code-container img {
                width: 120px;
                height: 120px;
            }
            .payment-methods {
                flex-direction: column;
            }
            .payment-methods .payment-method-btn {
                width: 100%;
            }
            .final-price-display {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
            .final-price-display .primary-btn.pay-now-btn {
                width: 100%;
                justify-content: center;
            }
            .security-assurance {
                flex-direction: column;
                gap: 4px;
            }
            .security-assurance .separator {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div id="notificationContainer"></div>
    <nav class="navbar">
        <div class="navbar-brand">
            <svg class="logo" width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg>
            <h1>WriterPro</h1>
        </div>
        <ul class="navbar-nav" id="navbarNav">
            <li><a href="index.html" class="nav-link active">首页</a></li>
            <li><a href="history.html" class="nav-link">历史记录</a></li>
            <li><a href="partnership.html" class="nav-link">招商加盟</a></li>
            <li><a href="#" class="nav-link" id="contactServiceBtn">联系客服</a></li>
        </ul>
        <div class="navbar-auth">
            <a href="login.html" id="loginBtn" class="auth-btn">登录 / 注册</a>
            <div class="user-menu" id="userMenu" style="display: none;">
                <div class="user-info" id="userMenuTrigger">
                    <div class="user-avatar">
                        <span id="userInitial">U</span>
                    </div>
                    <span class="username" id="navUsername">用户</span>
                    <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
                        <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="user-dropdown" id="userDropdown">
                    <div class="dropdown-item" id="changePasswordBtn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        修改密码
                    </div>
                    <div class="dropdown-item" id="logoutBtn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M9 21H5a2 2 0 01-2-2V5a2 2 0 012-2h4m7 14l5-5-5-5m5 5H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        退出登录
                    </div>
                </div>
            </div>
        </div>
        <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="切换菜单">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
    </nav>

    <main class="container">
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="hero-title-main">重新定义</span>
                    <span class="hero-title-accent">文本优化</span>
                </h1>
                <p class="hero-subtitle">
                    智能文本优化，让每一个字都精准有力
                </p>
                <p class="hero-description">
                    无论是学术、商业报告还是创意写作，我们都能帮您优化AI率
                </p>
            </div>
        </section>

        <div class="main-content">
            <div class="main-content-left">
                <div class="input-section card">
    <div class="tab-container">
        <div class="tab-buttons">
            <button class="tab-button active" data-tab="upload">上传文档</button>
            <button class="tab-button" data-tab="text">粘贴文本</button>
        </div>
        <div class="tab-content">
            <div id="uploadTabContent" class="tab-pane active" data-tab="upload">
                <div class="upload-area">
                    <input type="file" id="documentUpload" accept=".txt,.doc,.docx,.pdf" style="display: none;">
                    <div class="upload-icon-wrapper">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>
                    </div>
                    <h3 class="upload-title">文档上传区域</h3>
                    <p class="upload-subtitle">支持 .txt, .docx, 等格式</p>
                    <button id="uploadBtn" class="upload-btn primary-btn">
                        选择文件
                    </button>
                    <div class="upload-features">
                        <div class="feature-item">
                           <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg>
                        
                        
                        </div>
                    </div>
                </div>
                <div id="documentInfo" class="document-info" style="display: none;">
                    <span id="fileName"></span>
                    <span id="wordCount"></span>
                    <button id="clearDocBtn" class="clear-btn">&times;</button>
                </div>
            </div>
            <div id="textTabContent" class="tab-pane" data-tab="text">
              
               
                <textarea id="inputText" placeholder="请在此输入需要优化的文本..."></textarea>
                <div class="upload-features">
                    <div class="feature-item">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>
                        <span>智能识别</span>
                    </div>
                    <div class="feature-item">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path></svg>
                        <span>高效编辑</span>
                    </div>
                    <div class="feature-item">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 8h14M5 12h14M5 16h14M6 4h.01M18 4h.01M6 20h.01M18 20h.01M10 4h.01M14 4h.01M10 20h.01M14 20h.01"></path></svg>
                        <span>多语言支持</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="controls">
        <select id="optimizeType">
            <option value="academic">优化AI率</option>
        </select>
        <button id="submitBtn" class="primary-btn" style="display: none;">开始优化</button>
    </div>
</div>
                
                <div class="output-section card">
                    <h2>优化结果</h2>
                    <div id="outputText" class="output-box">
                        <p class="placeholder">优化后的文本将显示在这里...</p>
                    </div>
                </div>
            </div>

            <div class="main-content-right">
                <div id="aiServiceCard" class="service-card card">
                    <div class="badge-top-left">限时2折 质量升级不加价</div>
                    <div class="badge-top-right">新优惠</div>
                    <div class="card-header">
    <div class="service-info">
        <div class="service-icon">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="white" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
</div>
        <div class="service-title-group">
            <h3 class="main-service-name">内容优化</h3>
            <span class="badge-pro">PRO</span>
        </div>
    </div>
    <div class="price-info">
        <p class="current-price">¥0.01<span class="price-unit">/千字符</span></p>
        <p class="original-price">原价¥25</p>
    </div>
</div>
                    <div class="card-body">
    <p class="platform-support">支持平台：<span class="platform-name">知网 <span class="platform-sep">|</span> 维普 <span class="platform-sep">|</span> 万方 <span class="platform-sep">|</span> 格子达 <span class="platform-sep">|</span> Turnitin</span></p>
    <div class="feature-list">
        <div class="feature-item">
            <div class="feature-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-8.93" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M22 4L12 14.01l-3-3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="feature-content">
                <div class="feature-title">率值确保达标</div>
                <div class="feature-desc">AI率一键降至10%内，不达标全额退款</div>
            </div>
        </div>
        <div class="feature-item">
            <div class="feature-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-8.93" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M22 4L12 14.01l-3-3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="feature-content">
                <div class="feature-title">保持高质量表述</div>
                <div class="feature-desc">清北语言学家亲研，效果媲美行业专家</div>
            </div>
        </div>
    </div>
</div>
                    <div id="aiPriceDisplay" style="display: none;">
                        <div class="price-summary-box">
                            <span>总价: ¥<span id="aiCalculatedPrice">0</span></span>
                            <button class="primary-btn pay-now-btn-card" data-service="ai">立即支付</button>
                        </div>
                    </div>
                </div>

                <div id="expertServiceCard" class="service-card expert-service-card" tabindex="0" role="button" aria-label="专家人工润色服务">
                    <div class="service-badge badge-expert">
                        人工润色
                    </div>
                    
                    <div class="service-header">
                        <div class="service-title">
                            <div class="service-icon expert-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="">
                                    <path d="M22 10v6M2 10l10-5 10 5M2 16l10 5 10-5M2 10v6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div>
                                <h3 class="service-name">学术专家</h3>
                                <p class="service-subtitle"></p>
                            </div>
                        </div>
                        <div class="service-price">
                            <span class="current-price">¥50</span>
                            <span class="price-unit">/千字符</span>
                        </div>
                    </div>
                    
                    <div class="service-features">
                        <div class="feature-item">
                            <div class="feature-icon expert-star">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="">
                                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="feature-content">
                                <div class="feature-title">顶级学术专家团队</div>
                                <div class="feature-desc">覆盖100+学科领域，博士以上学历优化AI内容</div>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="">
                                    <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="feature-content">
                                <div class="feature-title">权威检测服务</div>
                                <div class="feature-desc">提供知网/万方官方送检，确保学术标准达标</div>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="">
                                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="feature-content">
                                <div class="feature-title">人工精细润色</div>
                                <div class="feature-desc">专业编辑逐句优化，保证流畅性和学术规范性</div>
                            </div>
                        </div>
                    </div>
                    <div id="expertPriceDisplay" style="display: none;">
                        <div class="price-summary-box">
                            <span>总价: ¥<span id="expertCalculatedPrice">0</span></span>
                            <button class="primary-btn pay-now-btn-card" data-service="expert">立即支付</button>
                        </div>
                    </div>
                    <button id="expertPayNowBtn" class="primary-btn pay-now-btn" style="display: none;">
                        ⚡ 扫码立即支付 ¥<span id="expertPayNowAmount">0</span>
                    </button>
                </div>

                <div class="payment-section card" style="display:none;">
                    <div class="card-header">
    <div class="payment-title-group">
        <div class="payment-icon-wrapper">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <rect x="1" y="4" width="22" height="16" rx="2" ry="2" 
                    stroke="currentColor" stroke-width="2"/>
              <line x1="1" y1="10" x2="23" y2="10" 
                    stroke="currentColor" stroke-width="2"/>
            </svg>
        </div>
        <div>
            <h3 class="payment-title">二维码支付</h3>
            <p class="payment-subtitle">安全便捷的支付方式</p>
        </div>
    </div>
    <div class="realtime-order-indicator" style="display: none;">
        <div class="pulse-dot"></div>
        <span>北京用户 • 3分钟前 • 内容优化</span>
    </div>
</div>

<!-- 主页面支付区域已删除，所有支付通过弹窗进行 -->

    <div class="security-assurance">
        <div class="icon-check-circle">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
        </div>
        <span>支付后立即开始处理</span>
        <span class="separator">•</span>
        <span>数据安全保护 • 不达标全额退款</span>
    </div>
</div>
                </div>
            </div>
        
        </div>
    </main>

    <div id="optimizationModal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>正在优化文档</h3>
            </div>
            <div class="modal-body">
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-bar-fill"></div>
                    </div>
                    <span id="progressPercent">0%</span>
                </div>
                <p id="progressStatus">准备开始...</p>
            </div>
        </div>
    </div>

    <!-- 极简页脚 -->
    <footer class="minimal-footer">
        <div class="footer-content">
            <div class="footer-brand">
                <div class="brand-name">WriterPro</div>
                <p class="brand-tagline">逐句精准优化，确保流畅自然，专业降AI率</p>
            </div>
            
            <div class="footer-right">
                <div class="footer-links">
                    <a href="privacy.html">隐私政策</a>
                    <a href="terms.html">服务条款</a>
                    <a href="#" id="footerContactUsBtn">联系我们</a>
                </div>
                
                <div class="footer-company">
                    <span>WriterPro</span>
                    <span class="separator">|</span>
                    <span>北京福强科技有限责任公司</span>
                    <span class="separator">|</span>
                    <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener">网站备案号：京ICP备2025129459号</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://unpkg.com/qrcode-generator@1.4.4/qrcode.js"></script>
    <script src="script.js?t=1753940000&premium-flow=true"></script>

    <!-- Payment Modal -->
    <div id="paymentModal" class="modal-overlay" style="display: none;">
        <div class="modal-content payment-modal-content">
            <div class="modal-header">
                <h3>确认支付</h3>
                <button class="modal-close" id="paymentModalClose">&times;</button>
            </div>
            <div class="modal-body">
                <!-- Top Title Area -->
                <div class="payment-card-header">
                    <div class="payment-title-group">
                        <div class="payment-icon-wrapper">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                              <rect x="1" y="4" width="22" height="16" rx="2" ry="2"
                                    stroke="currentColor" stroke-width="2"/>
                              <line x1="1" y1="10" x2="23" y2="10"
                                    stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="payment-title">二维码支付</h3>
                            <p class="payment-subtitle">安全便捷的支付方式</p>
                        </div>
                    </div>
                    <div class="realtime-order-indicator" style="display: none;">
                        <div class="pulse-dot"></div>
                        <span>北京用户 • 3分钟前 • 内容优化</span>
                    </div>
                </div>

                <!-- QR Code Display Area -->
                <div class="qr-code-display-area">
                    <div class="qr-code-placeholder">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="17" x2="12" y2="17"></line><line x1="12" y1="12" x2="12" y2="12"></line><line x1="12" y1="7" x2="12" y2="7"></line></svg>
                        <p>请先选择支付方式</p>
                        <p>点击支付宝或微信</p>
                    </div>

                    <!-- 通用二维码容器（用于兼容现有代码，永久隐藏） -->
                    <div class="qr-code-container" style="display: none !important;">
                        <img src="" alt="支付二维码" id="modalQrCodeImage">
                        <div class="qr-code-logo-overlay" id="modalQrCodeLogoOverlay">
                            <!-- WeChat or Alipay SVG will be inserted here by JS -->
                        </div>
                        <div class="qr-code-info">
                            <h4 class="qr-code-title">扫码支付 ¥<span id="modalQrCodeAmount">0</span></h4>
                            <p class="qr-code-service" id="modalQrCodeService">AI内容优化/专家润色服务</p>
                        </div>
                        <div class="security-tips">
                            <div class="tip-item star">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
                                <span>支付后立即开始处理</span>
                            </div>
                            <div class="tip-item check">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                <span>不达标全额退款</span>
                            </div>
                        </div>

                    </div>

                    <!-- 支付宝二维码容器 -->
                    <div class="qr-code-container" id="alipayQrContainer" style="display: none;">
                        <img src="" alt="支付宝二维码" id="alipayQrCodeImage">
                        <div class="qr-code-logo-overlay" id="alipayQrCodeLogoOverlay">
                            <!-- Alipay SVG will be inserted here by JS -->
                        </div>
                        <div class="qr-code-info">
                            <h4 class="qr-code-title">扫码支付 ¥<span id="alipayQrCodeAmount">0</span></h4>
                            <p class="qr-code-service">AI内容优化/专家润色服务</p>
                        </div>
                        <div class="security-tips">
                            <div class="tip-item star">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
                                <span>支付后立即开始处理</span>
                            </div>
                            <div class="tip-item check">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                <span>不达标全额退款</span>
                            </div>
                        </div>
                        <div class="countdown-timer">
                            二维码有效期：<span id="alipayCountdownTime">05:00</span>
                        </div>
                    </div>

                    <!-- 微信二维码容器 -->
                    <div class="qr-code-container" id="wechatQrContainer" style="display: none;">
                        <img src="" alt="微信二维码" id="wechatQrCodeImage">
                        <div class="qr-code-logo-overlay" id="wechatQrCodeLogoOverlay">
                            <!-- WeChat SVG will be inserted here by JS -->
                        </div>
                        <div class="qr-code-info">
                            <h4 class="qr-code-title">扫码支付 ¥<span id="wechatQrCodeAmount">0</span></h4>
                            <p class="qr-code-service">AI内容优化/专家润色服务</p>
                        </div>
                        <div class="security-tips">
                            <div class="tip-item star">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>
                                <span>支付后立即开始处理</span>
                            </div>
                            <div class="tip-item check">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                <span>不达标全额退款</span>
                            </div>
                        </div>
                        <div class="countdown-timer">
                            二维码有效期：<span id="wechatCountdownTime">05:00</span>
                        </div>
                    </div>
                </div>

                <!-- Bottom Payment Area -->
                <div class="final-price-display">
                    <p>服务价格: ¥<span id="finalServicePrice">0</span></p>
                </div>

                <!-- 邮箱收集区域 -->
                <div class="email-collection" style="margin: 20px 0; padding: 15px; background: #f8f9ff; border-radius: 8px; border: 1px solid #e1e5f2;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#666" stroke-width="2" style="margin-right: 8px;">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                            <polyline points="22,6 12,13 2,6"/>
                        </svg>
                        <label for="paymentEmail" style="font-size: 14px; color: #333; font-weight: 500;">邮箱地址（可选）</label>
                    </div>
                    <input type="email" id="paymentEmail" placeholder="用于接收订单通知和跨设备访问历史记录"
                           style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; box-sizing: border-box;">
                    <p style="font-size: 12px; color: #666; margin: 8px 0 0 0; line-height: 1.4;">
                        💡 填写邮箱后可在任意设备通过邮箱查看历史记录
                    </p>
                </div>

                <!-- 服务协议区域 -->
                <div class="agreement-section" style="margin: 15px 0;">
                    <label style="display: flex; align-items: flex-start; font-size: 13px; color: #666; cursor: pointer;">
                        <input type="checkbox" id="agreementCheckbox" style="margin-right: 8px; margin-top: 2px;">
                        <span>我已阅读并同意 <a href="#" style="color: #8A2BE2; text-decoration: none;" onclick="showServiceAgreement()">《用户服务协议》</a> 和 <a href="#" style="color: #8A2BE2; text-decoration: none;" onclick="showPrivacyPolicy()">《隐私政策》</a></span>
                    </label>
                </div>

                <!-- 协议提示区域 -->
                <div id="agreementWarning" style="display: none; color: #ff4444; font-size: 12px; margin: 5px 0; text-align: center;">
                    请先勾选同意协议
                </div>

                <!-- Payment Methods -->
                <div class="payment-methods">
                    <button class="payment-method-btn alipay active" data-method="alipay" style="display: none;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z" fill="#00A0E9"/>
                          <path d="M15.5 8.5h-7c-.552 0-1 .448-1 1v5c0 .552.448 1 1 1h7c.552 0 1-.448 1-1v-5c0-.552-.448-1-1-1z" fill="#fff"/>
                          <path d="M12 10.5c-.828 0-1.5.672-1.5 1.5s.672 1.5 1.5 1.5 1.5-.672 1.5-1.5-.672-1.5-1.5-1.5z" fill="#00A0E9"/>
                        </svg>
                        支付宝
                    </button>
                    <button class="payment-method-btn wechat" data-method="wechat" id="modalWechatPaymentBtn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"
                                stroke="#07C160" stroke-width="2"/>
                        </svg>
                        <span id="modalWechatPaymentText">微信</span>
                    </button>
                    <button class="payment-method-btn fuiou-alipay" data-method="fuiou-alipay" id="modalFuiouAlipayPaymentBtn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" fill="#1677FF"/>
                          <path d="M8 9h8v2H8V9zm0 4h8v2H8v-2z" fill="#fff"/>
                        </svg>
                        支付宝
                    </button>
                    <button class="payment-method-btn fuiou-wechat" data-method="fuiou-wechat" id="modalFuiouWechatPaymentBtn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" fill="#07C160"/>
                          <path d="M8 9h8v2H8V9zm0 4h8v2H8v-2z" fill="#fff"/>
                        </svg>
                        微信
                    </button>
                </div>

                <!-- 支付按钮区域 -->
                <div class="payment-buttons" style="margin-top: 15px; text-align: center;">
                    <!-- 支付宝直接支付按钮 -->
                    <button id="alipayDirectBtn" class="primary-btn" style="background-color: #00A0E9; width: 100%; padding: 12px; border: none; border-radius: 8px; color: white; font-weight: bold; cursor: pointer; display: none; margin-bottom: 10px;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="vertical-align: middle; margin-right: 8px;">
                          <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z" fill="#00A0E9"/>
                          <path d="M15.5 8.5h-7c-.552 0-1 .448-1 1v5c0 .552.448 1 1 1h7c.552 0 1-.448 1-1v-5c0-.552-.448-1-1-1z" fill="#fff"/>
                          <path d="M12 10.5c-.828 0-1.5.672-1.5 1.5s.672 1.5 1.5 1.5 1.5-.672 1.5-1.5-.672-1.5-1.5-1.5z" fill="#00A0E9"/>
                        </svg>
                        支付宝支付 ¥<span class="alipay-amount">0</span>
                    </button>

                    <!-- 微信支付直接支付按钮 -->
                    <button id="wechatDirectBtn" class="primary-btn" style="background-color: #07C160; width: 100%; padding: 12px; border: none; border-radius: 8px; color: white; font-weight: bold; cursor: pointer; display: none; margin-bottom: 10px;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="vertical-align: middle; margin-right: 8px;">
                          <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"
                                stroke="#ffffff" stroke-width="2" fill="none"/>
                        </svg>
                        微信支付 ¥<span class="wechat-amount">0</span>
                    </button>

                </div>

                <!-- Security Assurance Area -->
                <div class="security-assurance">
                    <div class="icon-check-circle">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    </div>
                    <span>支付后立即开始处理</span>
                    <span class="separator">•</span>
                    <span>数据安全保护 • 不达标全额退款</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 联系客服弹窗 -->
    <div id="contactModal" class="contact-modal">
        <div class="contact-modal-content">
            <div class="contact-modal-header">
                <h2 class="contact-modal-title">联系客服</h2>
                <button id="closeContactModal" class="contact-modal-close">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                </button>
            </div>

            <div class="contact-modal-body">
                <!-- 微信客服 -->
                <div class="contact-modal-section wechat">
                    <div class="section-header">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path></svg>
                        <h3>微信客服</h3>
                    </div>
                    
                    <div class="contact-modal-qrcode">
                        <div class="qrcode-inner">
                            <span>微信客服二维码</span>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600">扫描二维码添加客服微信，获得专业服务支持</p>
                </div>

                <!-- 工作时间 -->
                <div class="contact-modal-section info time">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                    <div class="info-content">
                        <p class="info-title">工作时间</p>
                        <p class="info-desc">周一至周日 9:00-18:00</p>
                    </div>
                </div>

                <!-- 邮箱联系 -->
                <div class="contact-modal-section info email">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline></svg>
                    <div class="info-content">
                        <p class="info-title">邮箱联系</p>
                        <p class="info-desc email"><EMAIL></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录提示覆盖层 -->
    <div id="loginPromptOverlay" class="login-prompt-overlay" style="display: none;">
        <div class="login-prompt-content">
            <div class="login-prompt-icon">🔐</div>
            <h3>需要登录</h3>
            <p>请先登录后再使用此功能</p>
            <div class="login-prompt-buttons">
                <button id="goToLoginBtn" class="btn btn-primary">立即登录</button>
                <button id="cancelLoginPrompt" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <style>
        .login-prompt-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        }

        .login-prompt-content {
            background: white;
            padding: 40px;
            border-radius: 16px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 90%;
        }

        .login-prompt-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .login-prompt-content h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 24px;
        }

        .login-prompt-content p {
            margin: 0 0 30px 0;
            color: #666;
            font-size: 16px;
        }

        .login-prompt-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .login-prompt-buttons .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .login-prompt-buttons .btn-primary {
            background: #007bff;
            color: white;
        }

        .login-prompt-buttons .btn-primary:hover {
            background: #0056b3;
        }

        .login-prompt-buttons .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .login-prompt-buttons .btn-secondary:hover {
            background: #545b62;
        }

        /* 用户菜单样式 */
        .user-menu {
            position: relative;
            display: inline-block;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-info:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        .username {
            color: white;
            font-weight: 500;
            font-size: 14px;
        }

        .dropdown-arrow {
            color: white;
            transition: transform 0.3s ease;
        }

        .user-info:hover .dropdown-arrow {
            transform: rotate(180deg);
        }

        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
            margin-top: 8px;
        }

        .user-menu:hover .user-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: #333;
            font-size: 14px;
        }

        .dropdown-item:hover {
            background-color: #f5f5f5;
        }

        .dropdown-item:first-child {
            border-radius: 8px 8px 0 0;
        }

        .dropdown-item:last-child {
            border-radius: 0 0 8px 8px;
        }

        .dropdown-item svg {
            width: 16px;
            height: 16px;
            color: #666;
        }

        /* 修改密码弹窗样式 */
        .password-modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .password-modal-content {
            background: white;
            border-radius: 12px;
            padding: 30px;
            width: 90%;
            max-width: 400px;
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .password-modal-content .close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
            transition: color 0.3s ease;
        }

        .password-modal-content .close:hover {
            color: #333;
        }

        .password-modal-content h2 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 20px;
            text-align: center;
        }

        .password-modal-content .form-group {
            margin-bottom: 20px;
        }

        .password-modal-content .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .password-modal-content .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .password-modal-content .form-group input:focus {
            outline: none;
            border-color: #ff6b35;
            box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1);
        }

        .password-modal-content .error-message {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
            display: block;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 25px;
        }

        .password-modal .cancel-btn {
            padding: 10px 20px;
            border: 1px solid #ddd;
            background: white;
            color: #666;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .password-modal .cancel-btn:hover {
            background: #f5f5f5;
            border-color: #ccc;
        }

        .password-modal .primary-btn {
            padding: 10px 20px;
            background: #ff6b35;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .password-modal .primary-btn:hover {
            background: #e55a2b;
        }
    </style>

    <!-- 修改密码弹窗 -->
    <div id="passwordModal" class="password-modal" style="display: none;">
        <div class="password-modal-content">
            <span class="close" id="closePasswordModal">&times;</span>
            <h2>修改密码</h2>
            <form id="changePasswordForm">
                <div class="form-group">
                    <label for="currentPassword">当前密码</label>
                    <input type="password" id="currentPassword" required>
                    <span class="error-message" id="currentPasswordError"></span>
                </div>
                <div class="form-group">
                    <label for="newPassword">新密码</label>
                    <input type="password" id="newPassword" required>
                    <span class="error-message" id="newPasswordError"></span>
                </div>
                <div class="form-group">
                    <label for="confirmNewPassword">确认新密码</label>
                    <input type="password" id="confirmNewPassword" required>
                    <span class="error-message" id="confirmNewPasswordError"></span>
                </div>
                <div class="form-actions">
                    <button type="button" class="cancel-btn" id="cancelPasswordBtn">取消</button>
                    <button type="submit" class="primary-btn">确认修改</button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>