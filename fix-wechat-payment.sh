#!/bin/bash

echo "🔧 开始修复富有支付微信支付权限问题..."

# 检查是否在正确的目录
if [ ! -f "backend/src/controllers/paymentController.js" ]; then
    echo "❌ 错误：请在项目根目录执行此脚本"
    exit 1
fi

echo "📁 当前目录：$(pwd)"

# 备份原文件
echo "💾 备份原文件..."
cp backend/src/controllers/paymentController.js backend/src/controllers/paymentController.js.backup
cp backend/src/config/fuiouConfig.js backend/src/config/fuiouConfig.js.backup

echo "✅ 备份完成"

# 修复控制器文件
echo "🔧 修复 paymentController.js..."

# 查找并替换控制器中的支付逻辑
sed -i 's/\/\/ 🚀 调用富有支付服务（先使用原接口测试）/\/\/ 🚀 调用富有支付服务/g' backend/src/controllers/paymentController.js

# 创建修复后的控制器逻辑
cat > /tmp/payment_fix.js << 'EOF'
      // 🚀 调用富有支付服务
      console.log('🔄 调用富有支付服务...');

      // 从请求中获取订单类型，默认为ALIPAY
      const orderType = req.body.orderType || 'ALIPAY';
      console.log(`📋 订单类型: ${orderType}`);

      let paymentResult;
      
      // ✅ 解决微信支付权限问题：2019年后新商户使用特殊封装接口
      const fuiouConfig = require('../config/fuiouConfig');
      const useSpecialInterface = fuiouConfig.shouldUseSpecialInterface(orderType);
      
      if (useSpecialInterface) {
        console.log(`🎯 ${orderType}支付：使用特殊封装接口（解决新商户权限问题）`);
        paymentResult = await FuiouService.createSpecialPayment({
          orderId: finalOrderId,
          amount: parseFloat(amount),
          description: description || '文档优化服务',
          orderType: orderType,
          clientIp: req.ip || '127.0.0.1'
        });
      } else {
        console.log(`💰 ${orderType}支付：使用标准接口`);
        paymentResult = await FuiouService.createPayment({
          orderId: finalOrderId,
          amount: parseFloat(amount),
          description: description || '文档优化服务',
          orderType: orderType,
          clientIp: req.ip || '127.0.0.1'
        });
      }
EOF

echo "🔧 修复 fuiouConfig.js..."

# 添加特殊接口配置
cat > /tmp/config_fix.js << 'EOF'
        // 特殊的封装接口地址（支持微信）
        // 解决2019年后新商户微信Native权限问题
        this.specialTestUrl = process.env.FUIOU_SPECIAL_TEST_URL || 'https://fundwx.fuiou.com';
        this.specialProdUrl = process.env.FUIOU_SPECIAL_PROD_URL || 'https://fundwx.fuiou.com';
        
        // 是否强制使用特殊接口（用于测试）
        this.forceSpecialInterface = process.env.FUIOU_FORCE_SPECIAL === 'true';
EOF

# 添加判断方法
cat > /tmp/config_method.js << 'EOF'
    // 判断是否应该使用特殊接口
    shouldUseSpecialInterface(orderType) {
        // 强制使用特殊接口（测试用）
        if (this.forceSpecialInterface) {
            return true;
        }
        
        // 微信支付使用特殊接口（解决新商户Native权限问题）
        if (orderType === 'WECHAT') {
            return true;
        }
        
        return false;
    }
EOF

echo "📝 创建环境变量配置示例..."
cat > .env.fuiou.example << 'EOF'
# 富有支付特殊接口配置（解决微信支付权限问题）
FUIOU_SPECIAL_TEST_URL=https://fundwx.fuiou.com
FUIOU_SPECIAL_PROD_URL=https://fundwx.fuiou.com
FUIOU_FORCE_SPECIAL=false

# 如果需要强制所有支付都使用特殊接口（测试用）
# FUIOU_FORCE_SPECIAL=true
EOF

echo "🔄 重启服务建议..."
cat > restart_services.sh << 'EOF'
#!/bin/bash
echo "🔄 重启后端服务..."
cd backend
npm install
pm2 restart all || npm start &

echo "🔄 重启前端服务..."
cd ../frontend
# 如果使用nginx
sudo nginx -s reload || echo "请手动重启nginx"

echo "✅ 服务重启完成"
EOF

chmod +x restart_services.sh

echo "✅ 修复脚本准备完成！"
echo ""
echo "📋 接下来的步骤："
echo "1. 手动应用代码修改（参考备份文件）"
echo "2. 更新环境变量：cp .env.fuiou.example .env"
echo "3. 重启服务：./restart_services.sh"
echo "4. 测试微信支付功能"
echo ""
echo "🔍 验证方法："
echo "- 查看日志中是否显示'使用特殊封装接口'"
echo "- 测试微信支付是否返回正确的微信二维码"
echo ""
echo "📞 如有问题，请检查："
echo "- 富有支付特殊接口地址是否正确"
echo "- 商户是否有特殊接口权限"
echo "- 网络连接是否正常"
